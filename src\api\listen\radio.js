import request from '@/utils/request'

// 查询电台列表
export function listRadio(query) {
  return request({
    url: '/system/radio/list',
    method: 'get',
    params: query
  })
}

// 查询电台详细
export function getRadio(radioId) {
  return request({
    url: '/system/radio/' + radioId,
    method: 'get'
  })
}

// 获取电台音频列表
export function getRadioAudios(radioId) {
  return request({
    url: '/system/radio/audio/' + radioId,
    method: 'get'
  })
}

// 获取电台下的音频列表 - 使用SysRadioStationController.getAudioList
export function getRadioAudioList(radioId) {
  return request({
    url: '/system/radio/audio/' + radioId,
    method: 'get'
  })
}

// 新增电台
export function addRadio(data) {
  return request({
    url: '/system/radio',
    method: 'post',
    data: data
  })
}

// 修改电台
export function updateRadio(data) {
  return request({
    url: '/system/radio',
    method: 'put',
    data: data
  })
}

// 删除电台
export function delRadio(radioId) {
  return request({
    url: '/system/radio/' + radioId,
    method: 'delete'
  })
}

// 导出电台
export function exportRadio(query) {
  return request({
    url: '/system/radio/export',
    method: 'get',
    params: query
  })
}

// 上传封面
export function uploadCover(file, customFormData) {
  // 如果提供了自定义的formData，则使用它，否则创建新的
  const formData = customFormData || new FormData()
  
  // 如果没有自定义formData，则需要添加file
  if (!customFormData) {
    formData.append('file', file)
  }
  
  return request({
    url: '/system/radio/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 修改电台状态
export function changeRadioStatus(radioId, status) {
  const data = {
    id: radioId,
    status: status
  }
  return request({
    url: '/system/radio/changeStatus',
    method: 'put',
    data: data
  })
}


// 修改电台推流状态
export function changeRadioPlaying(radioId, playing) {
  const data = {
    id: radioId,
    playing: playing
  }
  return request({
    url: '/system/radio/changePlaying',
    method: 'put',
    data: data
  })
}

// 修改电台置顶状态
export function changeRadioTop(radioId, isTop) {
  const data = {
    id: radioId,
    top: isTop
  }
  return request({
    url: '/system/radio/changeTop',
    method: 'put',
    data: data
  })
}

// 查询电台列表（下拉选择）
export function listAllRadio(query) {
  return request({
    url: '/system/radio/listAll',
    method: 'get',
    params: query
  })
}
