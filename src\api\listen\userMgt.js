import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/system/operate/user/list',
    method: 'get',
    params: query
  })
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: '/system/operate/user/' + parseStrEmpty(userId),
    method: 'get'
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/system/operate/user',
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/system/operate/user',
    method: 'put',
    data: data
  })
}

// 删除用户
export function delUser(userId) {
  return request({
    url: '/system/operate/user/' + userId,
    method: 'delete'
  })
}

// 批量删除用户
export function delUserBatch(userIds) {
  return request({
    url: '/system/operate/user/batch/' + userIds.join(','),
    method: 'delete'
  })
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    id: userId,
    status
  }
  return request({
    url: '/system/operate/user/changeStatus',
    method: 'put',
    data: data
  })
} 