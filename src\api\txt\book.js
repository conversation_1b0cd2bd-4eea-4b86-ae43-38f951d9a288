import request from '@/utils/request'

// 查询书籍列表
export function listBooks(query) {
  return request({
    url: '/system/books/list',
    method: 'get',
    params: query
  })
}

// 查询书籍详细
export function getBook(id) {
  return request({
    url: '/system/books/' + id,
    method: 'get'
  })
}

// 新增书籍
export function addBook(data) {
  return request({
    url: '/system/books',
    method: 'post',
    data: data
  })
}

// 修改书籍
export function updateBook(data) {
  return request({
    url: '/system/books',
    method: 'put',
    data: data
  })
}

// 删除书籍
export function delBook(ids) {
  return request({
    url: '/system/books/' + ids,
    method: 'delete'
  })
}

// 修改书籍状态
export function changeBookStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/system/books/changeStatus',
    method: 'put',
    data: data
  })
}

// 上传书籍封面图
export function uploadBookCover(file) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('type', 0) // 0表示封面图
  
  return request({
    url: '/system/books/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取书籍分类列表
export function listBookCategories() {
  return request({
    url: '/system/books/category/list',
    method: 'get'
  })
}

// 获取语言列表
export function listLanguages() {
  return request({
    url: '/system/books/languages',
    method: 'get'
  })
} 