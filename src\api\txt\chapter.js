import request from '@/utils/request'

// 查询章节列表
export function listChapters(query) {
  return request({
    url: '/system/chapter/list',
    method: 'get',
    params: query
  })
}

// 查询章节详细
export function getChapter(id) {
  return request({
    url: '/system/chapter/' + id,
    method: 'get'
  })
}

// 新增章节
export function addChapter(data) {
  return request({
    url: '/system/chapter',
    method: 'post',
    data: data
  })
}

// 修改章节
export function updateChapter(data) {
  return request({
    url: '/system/chapter',
    method: 'put',
    data: data
  })
}

// 删除章节
export function delChapter(ids) {
  return request({
    url: '/system/chapter/' + ids,
    method: 'delete'
  })
}

// 修改章节状态
export function changeChapterStatus(data) {
  return request({
    url: '/system/chapter/changeStatus',
    method: 'put',
    data: data
  })
}

// 批量修改章节状态
export function batchChangeChapterStatus(data) {
  return request({
    url: '/system/chapter/batchChangeStatus',
    method: 'put',
    data: data
  })
}

// 上传章节音频
export function uploadChapterAudio(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/system/chapter/uploadAudio',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 上传章节字幕
export function uploadChapterSubtitle(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/system/chapter/uploadSubtitle',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取章节文件列表
export function getChapterFiles(chapterId) {
  return request({
    url: '/system/chapter/files/' + chapterId,
    method: 'get'
  })
}

// 获取所有可用的语言列表
export function getLanguages() {
  return request({
    url: '/system/chapter/languages',
    method: 'get'
  })
}

// 删除上传文件
export function deleteChapterFile(fileUrl, fileId) {
  return request({
    url: '/system/chapter/deleteFile',
    method: 'delete',
    params: {
      fileUrl: fileUrl,
      fileId: fileId
    }
  })
} 