<template>
  <div class="audio-player-container">
    <div class="audio-player" :class="{ 'is-playing': isPlaying, 'compact-mode': true, 'empty-state': !currentAudioUrl }">
      <div class="player-content">
        <!-- 播放/暂停按钮 -->
        <el-button 
          :icon="isPlaying ? 'VideoPause' : 'VideoPlay'" 
          circle 
          size="small"
          class="control-btn play-btn"
          @click="togglePlay"
          :disabled="!currentAudioUrl"
        />
        
        <!-- 时间显示和进度条 -->
        <div class="progress-container">
          <div class="time-display">{{ formatTime(currentTime) }} / {{ formatTime(duration) }}</div>
          <el-slider 
            v-model="progress" 
            :max="100"
            class="progress-slider"
            @change="handleProgressChange"
            height="2"
            :disabled="!currentAudioUrl"
          />
        </div>
        
        <!-- 音量控制 -->
        <div class="volume-control" @mouseleave="hideVolumeSlider">
          <el-button 
            :icon="volume === 0 ? 'Mute' : 'Microphone'" 
            circle 
            size="small"
            class="control-btn volume-btn"
            @click="toggleVolumeControl"
            :disabled="!currentAudioUrl"
          />
          <div class="volume-slider-container" v-show="showVolumeSlider">
            <el-slider 
              v-model="volume" 
              :max="100"
              class="volume-slider"
              @change="handleVolumeChange"
              vertical
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 音频名称显示 -->
    <div class="audio-name-display" v-if="currentAudioName && currentAudioUrl">
      <el-tooltip :content="currentAudioName" placement="top" :show-after="500">
        <span class="audio-name-text">{{ currentAudioName }}</span>
      </el-tooltip>
    </div>
    
    <audio
      ref="audioEl"
      :src="currentAudioUrl"
      @timeupdate="onTimeUpdate"
      @loadedmetadata="onLoadedMetadata"
      @ended="onEnded"
      @error="onError"
      @play="onPlay"
      @pause="onPause"
    ></audio>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'

const props = defineProps({
  audioUrl: {
    type: [String, Array],
    required: true
  },
  audioName: {
    type: [String, Array],
    default: '未知音频'
  },
  autoplay: {
    type: Boolean,
    default: false
  },
  initialIndex: {
    type: Number,
    default: 0
  },
  singleMode: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['play', 'pause', 'ended', 'error', 'timeupdate', 'trackChange'])

const audioEl = ref(null)
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const volume = ref(100)
const lastVolume = ref(100)
const currentIndex = ref(props.initialIndex || 0)
const showVolumeSlider = ref(false)

// 处理音频列表
const audioList = computed(() => {
  if (!props.audioUrl) return []
  
  // 如果是字符串，转为单项数组
  if (typeof props.audioUrl === 'string') {
    return [{ url: props.audioUrl, name: props.audioName }]
  }
  
  // 如果是数组，格式化为对象数组
  if (Array.isArray(props.audioUrl)) {
    return props.audioUrl.map((url, index) => {
      let name = '未知音频'
      if (Array.isArray(props.audioName) && props.audioName[index]) {
        name = props.audioName[index]
      } else if (typeof props.audioName === 'string') {
        name = `${props.audioName} ${index + 1}`
      }
      return { url, name }
    })
  }
  
  return []
})

// 当前播放的音频URL和名称
const currentAudioUrl = computed(() => {
  if (audioList.value.length === 0) return ''
  const url = audioList.value[currentIndex.value]?.url || ''
  return url === '' ? null : url
})

const currentAudioName = computed(() => {
  if (audioList.value.length === 0) return '未知音频'
  return audioList.value[currentIndex.value]?.name || '未知音频'
})

// 进度计算，根据当前音频的时长动态变化
const progress = computed({
  get: () => (duration.value ? (currentTime.value / duration.value) * 100 : 0),
  set: (val) => handleProgressChange(val)
})

// 播放控制
const togglePlay = () => {
  if (isPlaying.value) {
    pause()
  } else {
    play()
  }
}

const play = async () => {
  if (!currentAudioUrl.value) return
  
  try {
    // 保存当前的时长，以便在播放后恢复
    const currentDuration = duration.value;
    
    await audioEl.value.play()
    
    // 如果播放前已经有时长，但播放后时长为0，则恢复之前的时长
    if (currentDuration > 0 && duration.value === 0) {
      duration.value = currentDuration;
    }
    
    // 确保音频元素加载完成后获取时长
    if (audioEl.value.readyState >= 1 && duration.value === 0) {
      duration.value = audioEl.value.duration || 0;
    }
  } catch (error) {
    console.error('播放失败:', error)
    emit('error', error)
  }
}

const pause = () => {
  if (audioEl.value) {
    audioEl.value.pause()
  }
}

// 切换到上一首
const playPrevious = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--
    resetPlayback()
    emit('trackChange', currentIndex.value)
  }
}

// 切换到下一首
const playNext = () => {
  if (currentIndex.value < audioList.value.length - 1) {
    currentIndex.value++
    resetPlayback()
    emit('trackChange', currentIndex.value)
  }
}

// 重置播放状态并开始播放
const resetPlayback = () => {
  currentTime.value = 0
  nextTick(() => {
    if (audioEl.value) {
      play()
    }
  })
}

// 音量控制
const toggleMute = () => {
  if (volume.value > 0) {
    lastVolume.value = volume.value
    volume.value = 0
  } else {
    volume.value = lastVolume.value
  }
}

// 显示/隐藏音量控制滑块
const toggleVolumeControl = () => {
  if (!currentAudioUrl.value) return
  showVolumeSlider.value = !showVolumeSlider.value
}

// 隐藏音量滑块
const hideVolumeSlider = () => {
  showVolumeSlider.value = false
}

const handleVolumeChange = (val) => {
  volume.value = val
  if (audioEl.value) {
    audioEl.value.volume = val / 100
  }
}

// 进度控制
const handleProgressChange = (val) => {
  if (audioEl.value && duration.value) {
    const time = (val / 100) * duration.value
    audioEl.value.currentTime = time
    currentTime.value = time
  }
}

// 事件处理
const onTimeUpdate = () => {
  if (audioEl.value) {
    currentTime.value = audioEl.value.currentTime
    
    // 确保在更新时间时也更新总时长（如果之前没有）
    if (audioEl.value.duration && duration.value === 0) {
      duration.value = audioEl.value.duration;
    }
    
    emit('timeupdate', currentTime.value)
  }
}

const onLoadedMetadata = () => {
  if (audioEl.value) {
    // 确保设置时长
    duration.value = audioEl.value.duration || 0;
    
    if (props.autoplay) {
      play()
    }
  }
}

const onPlay = () => {
  isPlaying.value = true
  
  // 确保播放时有正确的时长值
  if (audioEl.value && audioEl.value.duration && duration.value === 0) {
    duration.value = audioEl.value.duration;
  }
  
  emit('play')
}

const onPause = () => {
  isPlaying.value = false
  emit('pause')
}

const onEnded = () => {
  isPlaying.value = false
  
  if (props.singleMode) {
    emit('ended')
    return
  }
  
  if (currentIndex.value < audioList.value.length - 1) {
    playNext()
  } else {
    emit('ended')
  }
}

const onError = (error) => {
  // 检查是否为用户暂停引起的错误
  if (error && error.target && error.target.error && 
      error.target.error.name === "AbortError" && 
      error.target.error.message && 
      error.target.error.message.includes("interrupted by a call to pause")) {
    // 这是用户暂停引起的错误，不做处理，不触发error事件
    return;
  }
  
  isPlaying.value = false
  emit('error', error)
  
  if (currentIndex.value < audioList.value.length - 1) {
    console.warn(`音频 ${currentAudioName.value} 播放错误，尝试播放下一首`)
    playNext()
  }
}

// 工具函数
const formatTime = (time) => {
  if (!time) return '00:00'
  const minutes = Math.floor(time / 60)
  const seconds = Math.floor(time % 60)
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

// 组件生命周期
onMounted(() => {
  if (audioEl.value) {
    audioEl.value.volume = volume.value / 100
  }
})

onBeforeUnmount(() => {
  if (audioEl.value && !audioEl.value.paused) {
    audioEl.value.pause()
  }
})

// 监听 URL 变化
watch(() => props.audioUrl, () => {
  if (isPlaying.value) {
    pause()
  }
  currentIndex.value = props.initialIndex || 0
  currentTime.value = 0
  duration.value = 0
  if (props.autoplay) {
    nextTick(() => {
      play()
    })
  }
})

// 监听当前播放索引变化
watch(currentIndex, () => {
  currentTime.value = 0
  duration.value = 0
})

// 监听initialIndex变化
watch(() => props.initialIndex, (newIndex) => {
  if (currentIndex.value !== newIndex) {
    currentIndex.value = newIndex
    currentTime.value = 0
    duration.value = 0
    
    if (props.autoplay) {
      nextTick(() => {
        play()
      })
    }
  }
})

// 导出播放和暂停方法供父组件调用
defineExpose({
  play,
  pause,
  isPlaying
})
</script>

<style scoped>
.audio-player-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 100%;
  margin: 0;
}

.audio-player {
  background: #f0f2f5;
  border-radius: 20px;
  padding: 0 12px;
  box-shadow: var(--el-box-shadow-lighter);
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  position: relative;
}

.compact-mode {
  display: flex;
  width: 304px;
  height: 58px;
  align-items: center;
}

.player-content {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 10px 0;
}

.control-btn {
  flex-shrink: 0;
}

.play-btn, .volume-btn {
  font-size: 12px;
  width: 32px;
  height: 32px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 4px;
}

.time-display {
  font-size: 12px;
  color: var(--el-text-color-regular);
  text-align: left;
  margin-left: 2px;
}

.progress-slider {
  flex: 1;
  margin: 0;
}

.volume-control {
  display: flex;
  align-items: center;
  position: relative;
}

.volume-slider-container {
  position: absolute;
  bottom: 100%;
  right: 0;
  background-color: white;
  border-radius: 8px;
  padding: 12px 8px;
  box-shadow: var(--el-box-shadow-light);
  z-index: 10;
  height: 120px;
  width: 40px;
  display: flex;
  justify-content: center;
}

.volume-slider {
  height: 100px;
}

.play-btn {
  position: relative;
}

.is-playing .play-btn::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: var(--el-color-primary-light-9);
  animation: ripple 1s infinite;
  z-index: -1;
}

/* 音频名称显示样式 */
.audio-name-display {
  margin-top: 8px;
  padding: 0 4px;
  width: 100%;
  text-align: center;
}

.audio-name-text {
  font-size: 13px;
  color: var(--el-text-color-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  max-width: 100%;
  cursor: default;
}

/* 空状态样式 */
.empty-state .control-btn,
.empty-state .progress-slider {
  opacity: 0.6;
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0.4;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* 自定义进度条样式 */
:deep(.progress-slider .el-slider__runway) {
  height: 4px;
  background-color: #e4e7ed;
}

:deep(.progress-slider .el-slider__bar) {
  height: 4px;
  background-color: var(--el-color-primary);
}

:deep(.progress-slider .el-slider__button-wrapper) {
  height: 16px;
  width: 16px;
  top: -6px;
}

:deep(.progress-slider .el-slider__button) {
  height: 12px;
  width: 12px;
  border: 2px solid var(--el-color-primary);
}

/* 自定义音量滑块样式 */
:deep(.el-slider--vertical) {
  height: 100px;
}

:deep(.el-slider--vertical .el-slider__runway) {
  width: 4px;
  height: 100%;
}

:deep(.el-slider--vertical .el-slider__bar) {
  width: 4px;
}

:deep(.el-slider--vertical .el-slider__button-wrapper) {
  width: 24px;
  height: 24px;
  left: -10px;
  top: auto;
}

:deep(.el-slider--vertical .el-slider__button) {
  width: 12px;
  height: 12px;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .compact-mode {
    min-width: 240px;
  }
}

@media screen and (max-width: 576px) {
  .compact-mode {
    min-width: 200px;
  }
}
</style> 