<template>
  <div class="http-stream-player-container">
    <div class="http-stream-player" :class="{ 'is-playing': isPlaying, 'compact-mode': true, 'empty-state': !streamUrl }">
      <div class="player-content">
        <!-- 播放/暂停按钮 -->
        <el-button 
          :icon="isPlaying ? 'VideoPause' : 'VideoPlay'" 
          circle 
          size="small"
          class="control-btn play-btn"
          @click="togglePlay"
          :disabled="!streamUrl"
        />
        
        <!-- 状态显示 -->
        <div class="status-container">
          <div class="status-indicator" :class="connectionStatus">
            {{ statusText }}
          </div>
          
          <!-- 连接时长显示 -->
          <div class="connection-time" v-if="isPlaying && connectionDuration > 0">
            {{ formatTime(connectionDuration) }}
          </div>
        </div>
        
        <!-- 音量控制 -->
        <div class="volume-control" @mouseleave="hideVolumeSlider">
          <el-button 
            :icon="volume === 0 ? 'Mute' : 'Microphone'" 
            circle 
            size="small"
            class="control-btn volume-btn"
            @click="toggleVolumeControl"
            :disabled="!streamUrl"
          />
          <div class="volume-slider-container" v-show="showVolumeSlider">
            <el-slider 
              v-model="volume" 
              :max="100"
              class="volume-slider"
              @change="handleVolumeChange"
              vertical
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 音频名称显示 -->
    <div class="audio-name-display" v-if="audioName && streamUrl">
      <el-tooltip :content="audioName" placement="top" :show-after="500">
        <span class="audio-name-text">{{ audioName }}</span>
      </el-tooltip>
    </div>
    
    <!-- 使用video标签播放FLV流 - 不使用v-show或v-if -->
    <video
      ref="videoEl"
      class="flv-player"
      preload="auto"
      :muted="false"
      @error="onVideoError"
      @play="onPlay"
      @pause="onPause"
      @canplay="onCanPlay"
      @waiting="onWaiting"
      @playing="onPlaying"
      @stalled="onStalled"
      @suspend="onSuspend"
      @abort="onAbort"
    ></video>
    
    <!-- 原有的audio标签用于非FLV流 -->
    <audio
      v-if="!isFlvStream"
      ref="audioEl"
      :src="streamUrl"
      preload="auto"
      @error="onError"
      @play="onPlay"
      @pause="onPause"
      @canplay="onCanPlay"
      @waiting="onWaiting"
      @playing="onPlaying"
      @stalled="onStalled"
      @suspend="onSuspend"
      @abort="onAbort"
    ></audio>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import flvjs from 'flv.js'

// 立即禁用flv.js的所有日志输出
if (flvjs && flvjs.LoggingControl) {
  flvjs.LoggingControl.enableAll = false;
  flvjs.LoggingControl.enableError = false;
  flvjs.LoggingControl.enableWarn = false;
  flvjs.LoggingControl.enableInfo = false;
  flvjs.LoggingControl.enableDebug = false;
}

const props = defineProps({
  streamUrl: {
    type: String,
    required: true,
    default: ''
  },
  audioName: {
    type: String,
    default: '实时音频流'
  },
  autoplay: {
    type: Boolean,
    default: false
  },
  showStats: {
    type: Boolean,
    default: false
  },
  reconnectAttempts: {
    type: Number,
    default: 3
  },
  reconnectInterval: {
    type: Number,
    default: 5000
  }
})

const emit = defineEmits([
  'play', 
  'pause', 
  'error', 
  'connected', 
  'disconnected', 
  'waiting', 
  'stalled',
  'reconnecting',
  'reconnected',
  'reconnect-failed'
])

// 状态变量
const audioEl = ref(null)
const videoEl = ref(null)
const flvPlayer = ref(null)
const isPlaying = ref(false)
const volume = ref(100)
const lastVolume = ref(100)
const showVolumeSlider = ref(false)
const connectionStatus = ref('disconnected')
const bufferingState = ref('未缓冲')
const connectionStartTime = ref(null)
const connectionDuration = ref(0)
const reconnectCount = ref(0)
const reconnectTimer = ref(null)
const lastStreamUrl = ref('')
const isPlayerInitialized = ref(false)
const isUserPaused = ref(false)

// 判断是否为FLV流
const isFlvStream = computed(() => {
  return props.streamUrl && props.streamUrl.toLowerCase().endsWith('.flv')
})

// 连接状态文本
const statusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return '已连接'
    case 'connecting':
      return '连接中...'
    case 'disconnected':
      return '未连接'
    case 'error':
      return '无法播放'
    default:
      return '未知状态'
  }
})

// 播放控制
const togglePlay = () => {
  if (isPlaying.value) {
    pause()
  } else {
    play()
  }
}

// 播放
const play = async () => {
  if (!props.streamUrl) {
    ElMessage.warning('未提供音频流地址')
    return
  }
  
  connectionStatus.value = 'connecting'
  
  try {
    // 保存当前播放状态，以便判断是否是从暂停恢复
    const wasPaused = !isPlaying.value;
    
    if (isFlvStream.value) {
      await playFlvStream()
    } else if (audioEl.value) {
      await audioEl.value.play()
    } else {
      throw new Error('播放器未初始化')
    }
    
    // 如果是从暂停恢复，重新启动连接计时器
    if (wasPaused) {
      startConnectionTimer()
    }
    
    // 确保播放后连接状态为已连接
    connectionStatus.value = 'connected';
    isPlaying.value = true;
  } catch (error) {
    connectionStatus.value = 'error'
    // 使用统一的错误提示
    emit('error', { 
      originalError: error,
      message: '请检查电台播放流'
    })
    isPlaying.value = false
  }
}

// 处理浏览器自动播放策略限制
const handleAutoplayRestriction = async () => {
  if (!videoEl.value) return false
  
  try {
    // 尝试播放静音视频，这通常会被允许
    videoEl.value.muted = true
    await videoEl.value.play()
    
    // 成功播放后，取消静音并继续播放
    videoEl.value.muted = false
    return true
  } catch (error) {
    return false
  }
}

// 播放FLV流
const playFlvStream = async () => {
  try {
    if (!videoEl.value) {
      throw new Error('播放器初始化失败，请刷新页面重试')
    }
    
    if (!flvjs || typeof flvjs.isSupported !== 'function') {
      throw new Error('视频播放组件未正确加载，请刷新页面重试')
    }
    
    if (!flvjs.isSupported()) {
      throw new Error('您的浏览器不支持FLV播放，请使用Chrome、Firefox或Edge浏览器')
    }
    
    // 如果URL相同且播放器已初始化，直接恢复播放
    if (isPlayerInitialized.value && lastStreamUrl.value === props.streamUrl && flvPlayer.value) {
      try {
        await videoEl.value.play()
        isPlaying.value = true
        connectionStatus.value = 'connected'
        emit('play')
        emit('connected')
        return
      } catch (error) {
        // 禁用错误日志输出
        // console.warn('恢复播放失败，尝试重新创建播放器:', error)
      }
    }
    
    // 如果URL不同或播放器未初始化，创建新的播放器实例
    if (lastStreamUrl.value !== props.streamUrl || !isPlayerInitialized.value) {
      // 销毁旧的播放器实例
      destroyFlvPlayer()
      
      // 更新最后使用的流地址
      lastStreamUrl.value = props.streamUrl
      
      // 创建新的播放器实例
      flvPlayer.value = flvjs.createPlayer({
        type: 'flv',
        url: props.streamUrl,
        isLive: true,
        hasAudio: true,
        hasVideo: false,
        cors: true,
        withCredentials: false,
        enableStashBuffer: true,
        stashInitialSize: 128,
        enableWorker: true,
        lazyLoad: false,
        fixAudioTimestampGap: true,
        accurateSeek: false,
        seekType: 'range',
        rangeLoadZeroStart: true,
        autoCleanupSourceBuffer: false,
        autoCleanupMaxBackwardDuration: 30,
        autoCleanupMinBackwardDuration: 10,
        reuseRedirectedURL: true,
        // 禁用日志输出
        debug: false,
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })
      
      if (!flvPlayer.value) {
        throw new Error('播放器创建失败，请稍后重试')
      }
      
      // 附加到视频元素
      flvPlayer.value.attachMediaElement(videoEl.value)
      
      // 绑定事件
      flvPlayer.value.on(flvjs.Events.ERROR, onFlvError)
      flvPlayer.value.on(flvjs.Events.LOADING_COMPLETE, onFlvLoaded)
      flvPlayer.value.on(flvjs.Events.MEDIA_INFO, onFlvMediaInfo)
      
      // 标记播放器已初始化
      isPlayerInitialized.value = true
    }
    
    // 设置音量
    videoEl.value.volume = volume.value / 100
    
    // 加载并播放
    flvPlayer.value.load()
    await videoEl.value.play()
    
    isPlaying.value = true
    connectionStatus.value = 'connected'
    emit('play')
    emit('connected')
    
  } catch (error) {
    destroyFlvPlayer()
    throw error
  }
}

// 销毁FLV播放器
const destroyFlvPlayer = () => {
  try {
    if (videoEl.value) {
      videoEl.value.pause()
    }
    
    if (flvPlayer.value) {
      flvPlayer.value.pause()
      flvPlayer.value.unload()
      flvPlayer.value.detachMediaElement()
      flvPlayer.value.destroy()
    }
  } catch (error) {
    // 禁用错误日志输出
    // console.error('销毁播放器时发生错误:', error)
  } finally {
    flvPlayer.value = null
    isPlayerInitialized.value = false
  }
}

// FLV播放器事件处理
const onFlvError = (errorType, errorDetail) => {
  if (isUserPaused.value) {
    return;
  }
  
  connectionStatus.value = 'error'
  // 发送错误事件，使用更友好的提示信息
  emit('error', {
    originalError: { type: errorType, detail: errorDetail },
    message: '请检查电台播放流'
  })
  isPlaying.value = false
}

const onFlvLoaded = () => {
  // 确保视频元素不是静音状态
  if (videoEl.value) {
    videoEl.value.muted = false
  }
}

const onFlvMediaInfo = (mediaInfo) => {
  
  // 检查是否有音频轨道
  if (mediaInfo && mediaInfo.audioDataRate) {
    // 禁用日志输出
    // 不打印任何媒体信息
  } else {
    // 禁用日志输出
    // console.warn('未检测到音频轨道或音频数据速率')
  }
  
  // 确保视频元素不是静音状态
  if (videoEl.value) {
    videoEl.value.muted = false
    
    // 尝试设置音量
    try {
      videoEl.value.volume = volume.value / 100
    } catch (error) {
      // 禁用日志输出
      // console.warn('设置音量失败:', error)
    }
  }
}

// 错误信息转换为中文
const getChineseErrorMessage = (error) => {
  const errorMessage = error.message || '未知错误'
  
  // 常见错误类型转换
  if (errorMessage.includes('NotSupportedError') || errorMessage.includes('Failed to load')) {
    return '音频格式不支持或资源无法访问'
  }
  
  if (errorMessage.includes('NotAllowedError')) {
    return '浏览器禁止自动播放，请手动点击播放按钮'
  }
  
  if (errorMessage.includes('AbortError')) {
    return '播放被中断'
  }
  
  if (errorMessage.includes('NetworkError') || errorMessage.includes('Bad Gateway')) {
    return '网络错误，无法连接到音频服务器'
  }
  
  if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
    return '连接超时，请检查网络状态'
  }
  
  return '音频播放出错，请稍后再试'
}

// 暂停
const pause = () => {
  isUserPaused.value = true;
  
  if (isFlvStream.value) {
    try {
      if (videoEl.value) {
        videoEl.value.pause()
      }
      isPlaying.value = false
      connectionStatus.value = 'disconnected'
      stopConnectionTimer()
      emit('pause')
    } catch (error) {
      // 禁用错误日志输出
      // console.error('暂停播放失败:', error)
    }
  } else if (audioEl.value) {
    audioEl.value.pause()
    connectionStatus.value = 'disconnected'
    stopConnectionTimer()
  }
  
  setTimeout(() => {
    isUserPaused.value = false;
  }, 300);
}

// 连接计时器
const startConnectionTimer = () => {
  connectionStartTime.value = Date.now()
  updateConnectionDuration()
}

const stopConnectionTimer = () => {
  connectionStartTime.value = null
  connectionDuration.value = 0
}

const updateConnectionDuration = () => {
  if (!connectionStartTime.value) return
  
  connectionDuration.value = Math.floor((Date.now() - connectionStartTime.value) / 1000)
  setTimeout(updateConnectionDuration, 1000)
}

// 显示/隐藏音量控制滑块
const toggleVolumeControl = () => {
  if (!props.streamUrl) return
  showVolumeSlider.value = !showVolumeSlider.value
}

// 隐藏音量滑块
const hideVolumeSlider = () => {
  showVolumeSlider.value = false
}

// 音量控制
const handleVolumeChange = (val) => {
  if (isFlvStream.value && videoEl.value) {
    try {
      // 使用videoEl而不是flvPlayer
      videoEl.value.volume = val / 100
      lastVolume.value = val
    } catch (error) {
      // 禁用日志输出
      // console.warn('设置视频音量失败:', error)
    }
  } else if (audioEl.value) {
    audioEl.value.volume = val / 100
    lastVolume.value = val
  }
}

// 静音切换
const toggleMute = () => {
  if (volume.value > 0) {
    lastVolume.value = volume.value
    volume.value = 0
  } else {
    volume.value = lastVolume.value
  }
  handleVolumeChange(volume.value)
}

// 事件处理
const onPlay = () => {
  isPlaying.value = true
  
  // 确保播放时连接状态更新为已连接
  if (connectionStatus.value !== 'connected') {
    connectionStatus.value = 'connected'
  }
  
  emit('play')
}

const onPause = () => {
  isPlaying.value = false
  emit('pause')
}

const onError = (error) => {
  if (isUserPaused.value) {
    return;
  }
  
  connectionStatus.value = 'error'
  // 获取错误详情
  const errorCode = error.target?.error?.code
  const errorMessage = error.target?.error?.message
  // 使用统一的错误提示
  const chineseErrorMessage = '请检查电台播放流'
  // 发送错误事件
  emit('error', {
    originalError: error,
    code: errorCode,
    message: chineseErrorMessage
  })
  stopConnectionTimer()
  isPlaying.value = false
}

const onCanPlay = () => {
  bufferingState.value = '已缓冲'
}

const onWaiting = () => {
  bufferingState.value = '缓冲中...'
  emit('waiting')
}

const onPlaying = () => {
  connectionStatus.value = 'connected'
  
  // 如果没有启动连接计时器，则启动它
  if (isPlaying.value && !connectionStartTime.value) {
    startConnectionTimer()
  }
  
  emit('connected')
}

const onStalled = () => {
  bufferingState.value = '已停滞'
  emit('stalled')
}

const onSuspend = () => {
  if (isPlaying.value) {
    bufferingState.value = '已暂停下载'
  }
}

const onAbort = () => {
  connectionStatus.value = 'disconnected'
  emit('disconnected')
  stopConnectionTimer()
}

// 视频错误处理
const onVideoError = (error) => {
  if (isUserPaused.value) {
    return;
  }
  
  connectionStatus.value = 'error'
  // 获取错误详情
  const errorCode = error.target?.error?.code
  const errorMessage = error.target?.error?.message
  // 使用统一的错误提示
  const chineseErrorMessage = '请检查电台播放流'
  // 发送错误事件
  emit('error', {
    originalError: error,
    code: errorCode,
    message: chineseErrorMessage
  })
  stopConnectionTimer()
  isPlaying.value = false
}

// 工具函数
const formatTime = (seconds) => {
  if (!seconds) return '00:00:00'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  return [
    hours.toString().padStart(2, '0'),
    minutes.toString().padStart(2, '0'),
    secs.toString().padStart(2, '0')
  ].join(':')
}

// 监听音量变化
watch(volume, (newVal) => {
  if (isFlvStream.value && videoEl.value) {
    try {
      // 使用videoEl而不是flvPlayer
      videoEl.value.volume = newVal / 100
    } catch (error) {
      // 禁用日志输出
      // console.warn('设置视频音量失败:', error)
    }
  } else if (audioEl.value) {
    audioEl.value.volume = newVal / 100
  }
})

// 监听 streamUrl 变化
watch(() => props.streamUrl, (newUrl, oldUrl) => {
  if (newUrl && newUrl !== oldUrl) {
    // 重置状态
    connectionStatus.value = 'disconnected'
    bufferingState.value = '未缓冲'
    stopConnectionTimer()
    
    // 如果当前正在播放，则重新加载并播放
    const wasPlaying = isPlaying.value
    
    // 清除重连计时器
    if (reconnectTimer.value) {
      clearTimeout(reconnectTimer.value)
      reconnectTimer.value = null
    }
    
    reconnectCount.value = 0
    
    // 处理FLV播放器
    if (isFlvStream.value) {
      // 如果URL相同，不销毁播放器
      if (lastStreamUrl.value !== newUrl) {
        destroyFlvPlayer()
      }
    } else if (audioEl.value) {
      try {
        audioEl.value.load()
      } catch (e) {
        // 禁用日志输出
        // console.warn('加载音频失败:', e)
      }
    }
    
    if (wasPlaying || props.autoplay) {
      connectionStatus.value = 'connecting'
      setTimeout(() => {
        play().catch(error => {
          // 禁用日志输出
          // console.error('播放失败:', error)
        })
      }, 300)
    }
  }
})

// 组件挂载
onMounted(() => {
  // 确保DOM已完全挂载
  nextTick(() => {
    // 确保音频元素音量设置正确
    if (audioEl.value) {
      try {
        audioEl.value.volume = volume.value / 100
      } catch (e) {
        // 禁用日志输出
        // console.warn('设置音频音量失败:', e)
      }
    }
    
    // 如果设置了自动播放，则尝试播放
    if (props.autoplay && props.streamUrl) {
      setTimeout(() => {
        play().catch(error => {
          // 禁用错误日志，不显示任何错误
        })
      }, 500)
    }
  })
})

// 组件卸载前
onBeforeUnmount(() => {
  if (reconnectTimer.value) {
    clearTimeout(reconnectTimer.value)
    reconnectTimer.value = null
  }
  
  if (isFlvStream.value) {
    destroyFlvPlayer()
  }
})

// 组件卸载
onUnmounted(() => {
  if (isFlvStream.value) {
    destroyFlvPlayer()
  } else if (audioEl.value && isPlaying.value) {
    audioEl.value.pause()
    audioEl.value.src = ''
  }
  stopConnectionTimer()
})

// 导出方法供父组件调用
defineExpose({
  play,
  pause,
  isPlaying,
  connectionStatus
})
</script>

<style scoped>
.http-stream-player-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 100%;
  margin: 0;
}

.http-stream-player {
  background: #f0f2f5;
  border-radius: 20px;
  padding: 0 12px;
  box-shadow: var(--el-box-shadow-lighter);
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  position: relative;
}

.compact-mode {
  display: flex;
  width: 304px;
  height: 58px;
  align-items: center;
}

.player-content {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 10px 0;
}

.control-btn {
  flex-shrink: 0;
}

.play-btn, .volume-btn {
  font-size: 12px;
  width: 32px;
  height: 32px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  overflow: hidden;
}

.status-indicator {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  white-space: nowrap;
}

.status-indicator.connected {
  background-color: var(--el-color-success);
  color: white;
}

.status-indicator.connecting {
  background-color: var(--el-color-warning);
  color: white;
}

.status-indicator.disconnected {
  background-color: var(--el-color-info);
  color: white;
}

.status-indicator.error {
  background-color: var(--el-color-danger);
  color: white;
}

.status-indicator.reconnecting {
  background-color: var(--el-color-warning);
  color: white;
  animation: blink 1s infinite;
}

.connection-time {
  font-size: 12px;
  color: var(--el-text-color-regular);
  white-space: nowrap;
}

.volume-control {
  display: flex;
  align-items: center;
  position: relative;
}

.volume-slider-container {
  position: absolute;
  bottom: 100%;
  right: 0;
  background-color: white;
  border-radius: 8px;
  padding: 12px 8px;
  box-shadow: var(--el-box-shadow-light);
  z-index: 10;
  height: 120px;
  width: 40px;
  display: flex;
  justify-content: center;
}

.volume-slider {
  height: 100px;
}

.play-btn {
  position: relative;
}

.is-playing .play-btn::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: var(--el-color-primary-light-9);
  animation: ripple 1s infinite;
  z-index: -1;
}

/* 音频名称显示样式 */
.audio-name-display {
  margin-top: 8px;
  padding: 0 4px;
  width: 100%;
  text-align: center;
}

.audio-name-text {
  font-size: 13px;
  color: var(--el-text-color-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  max-width: 100%;
  cursor: default;
}

/* 空状态样式 */
.empty-state .control-btn {
  opacity: 0.6;
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0.4;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* 自定义音量滑块样式 */
:deep(.el-slider--vertical) {
  height: 100px;
}

:deep(.el-slider--vertical .el-slider__runway) {
  width: 4px;
  height: 100%;
}

:deep(.el-slider--vertical .el-slider__bar) {
  width: 4px;
}

:deep(.el-slider--vertical .el-slider__button-wrapper) {
  width: 24px;
  height: 24px;
  left: -10px;
  top: auto;
}

:deep(.el-slider--vertical .el-slider__button) {
  width: 12px;
  height: 12px;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .compact-mode {
    min-width: 240px;
  }
  
  .status-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

@media screen and (max-width: 576px) {
  .compact-mode {
    min-width: 200px;
  }
}

.flv-player {
  position: absolute;
  width: 1px;
  height: 1px;
  overflow: hidden;
  opacity: 0;
  pointer-events: none;
}
</style> 