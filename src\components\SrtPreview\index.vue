<template>
  <div class="srt-preview-component" :class="{ 'fullscreen': isFullscreen }">
    <!-- 顶部操作栏 -->
    <div class="preview-header" v-if="showHeader">
      <span class="file-name" v-if="fileName">{{ fileName }}</span>
      <div class="preview-actions">
        <el-tooltip content="检查问题" placement="top" v-if="showCheck">
          <el-button size="small" type="info" @click="checkSrtIssues" :disabled="!parsedData.length">
            <el-icon><Warning /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="全屏查看" placement="top" v-if="showFullscreen">
          <el-button size="small" type="primary" @click="toggleFullscreen">
            <el-icon><FullScreen v-if="!isFullscreen" /><Aim v-else /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="下载SRT文件" placement="top" v-if="showDownload">
          <el-button size="small" type="success" @click="downloadSrt" :disabled="!parsedData.length">
            <el-icon><Download /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="关闭预览" placement="top" v-if="showClose">
          <el-button size="small" type="danger" @click="closePreview">
            <el-icon><Close /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- SRT文件内容预览 -->
    <div class="srt-content" :style="{ maxHeight: maxHeight }">
      <div v-if="parsedData.length > 0" class="srt-entries">
        <div v-for="(subtitle, index) in parsedData" :key="index" class="srt-entry">
          <div class="srt-index">{{ subtitle.index }}</div>
          <div 
            :class="['srt-time', { 'time-discontinuous': subtitle.hasGap }]"
            :title="subtitle.hasGap ? `与下一字幕有${subtitle.gapDuration}秒间隔` : ''"
          >
            {{ subtitle.startTime }} --> {{ subtitle.endTime }}
          </div>
          <div class="srt-text">{{ subtitle.text }}</div>
          <div v-if="subtitle.hasGap" class="srt-gap-indicator">
            <div class="gap-line"></div>
            <span class="gap-text">{{ subtitle.gapDuration }}秒间隔</span>
            <div class="gap-line"></div>
          </div>
        </div>
      </div>
      <div v-else-if="loading" class="loading-placeholder">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <span>加载SRT文件中...</span>
      </div>
      <div v-else class="empty-placeholder">
        <el-icon><Document /></el-icon>
        <span>{{ emptyText }}</span>
      </div>
    </div>

    <!-- 问题检查结果对话框 -->
    <el-dialog
      v-model="issuesDialogVisible"
      title="SRT文件问题检查"
      width="500px"
    >
      <div class="issues-content">
        <el-alert
          v-if="srtIssues.length === 0"
          type="success"
          :closable="false"
          title="未发现问题"
          description="SRT文件格式正确，未检测到明显的时间轴问题。"
          show-icon
        />
        <el-alert
          v-else
          type="warning"
          :closable="false"
          title="检测到以下问题"
          show-icon
        />
        <div v-if="srtIssues.length > 0" class="issues-list">
          <div v-for="(issue, index) in srtIssues" :key="index" class="issue-item">
            <el-tag size="small" type="warning" class="issue-tag">问题 {{ index + 1 }}</el-tag>
            <span>{{ issue }}</span>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="issuesDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { FullScreen, Aim, Warning, Download, Close, Document, Loading } from '@element-plus/icons-vue';

const props = defineProps({
  // SRT文件内容
  content: {
    type: String,
    default: ''
  },
  // 文件名称
  fileName: {
    type: String,
    default: ''
  },
  // 是否显示顶部操作栏
  showHeader: {
    type: Boolean,
    default: true
  },
  // 是否显示全屏按钮
  showFullscreen: {
    type: Boolean,
    default: true
  },
  // 是否显示下载按钮
  showDownload: {
    type: Boolean,
    default: true
  },
  // 是否显示检查按钮
  showCheck: {
    type: Boolean,
    default: true
  },
  // 是否显示关闭按钮
  showClose: {
    type: Boolean,
    default: true
  },
  // 最大高度
  maxHeight: {
    type: String,
    default: '500px'
  },
  // 空内容提示文本
  emptyText: {
    type: String,
    default: '暂无SRT文件内容'
  },
  // 是否立即解析内容
  immediate: {
    type: Boolean,
    default: true
  },
  // 是否正在加载
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'download', 'check-issues']);

// 解析后的SRT数据
const parsedData = ref([]);
// 全屏状态
const isFullscreen = ref(false);
// 问题检查对话框
const issuesDialogVisible = ref(false);
// SRT文件问题列表
const srtIssues = ref([]);

// 监听内容变化
watch(() => props.content, (newContent) => {
  if (newContent && props.immediate) {
    parseSrtContent(newContent);
  }
}, { immediate: props.immediate });

// 在组件挂载时添加ESC键监听，用于退出全屏
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown);
});

// 在组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown);
});

// 处理键盘事件
function handleKeyDown(e) {
  if (e.key === 'Escape' && isFullscreen.value) {
    isFullscreen.value = false;
  }
}

// 解析SRT内容
function parseSrtContent(content) {
  if (!content || content.trim() === '') {
    parsedData.value = [];
    return;
  }
  
  const lines = content.split('\n');
  const subtitles = [];
  
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].trim() === '') continue;
    
    const index = lines[i].trim();
    if (!isNaN(index)) {
      i++;
      if (i < lines.length) {
        const timeLine = lines[i];
        const timeMatch = timeLine.match(/(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})/);
        if (timeMatch) {
          const startTime = timeMatch[1];
          const endTime = timeMatch[2];
          i++;
          let text = '';
          while (i < lines.length && lines[i].trim() !== '') {
            text += lines[i] + '\n';
            i++;
          }
          
          subtitles.push({
            index: parseInt(index),
            startTime,
            endTime,
            startSeconds: timeStringToSeconds(startTime),
            endSeconds: timeStringToSeconds(endTime),
            text: text.trim(),
            hasGap: false,
            gapDuration: 0
          });
        }
      }
    }
  }
  
  // 按开始时间排序字幕
  subtitles.sort((a, b) => a.startSeconds - b.startSeconds);
  
  // 检查时间间隔，标记不连续的部分
  for (let i = 0; i < subtitles.length - 1; i++) {
    const currentEnd = subtitles[i].endSeconds;
    const nextStart = subtitles[i + 1].startSeconds;
    const gap = nextStart - currentEnd;
    
    if (gap > 1) { // 如果间隔大于1秒，标记为不连续
      subtitles[i].hasGap = true;
      subtitles[i].gapDuration = Math.round(gap);
    }
  }
  
  parsedData.value = subtitles;
}

// 字符串时间格式转换为秒数
function timeStringToSeconds(timeStr) {
  const [time, milliseconds] = timeStr.split(',');
  const [hours, minutes, seconds] = time.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds + Number(milliseconds) / 1000;
}

// 秒数转换为时间字符串
function secondsToTimeString(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
}

// 切换全屏状态
function toggleFullscreen() {
  isFullscreen.value = !isFullscreen.value;
}

// 关闭预览
function closePreview() {
  isFullscreen.value = false;
  emit('close');
}

// 检查SRT文件问题
function checkSrtIssues() {
  srtIssues.value = [];
  
  // 没有数据时不检查
  if (!parsedData.value.length) {
    ElMessage.warning('没有可检查的SRT内容');
    return;
  }
  
  // 检查字幕序号是否连续
  for (let i = 0; i < parsedData.value.length; i++) {
    if (parsedData.value[i].index !== i + 1) {
      srtIssues.value.push(`字幕序号不连续，第${i + 1}条字幕序号为${parsedData.value[i].index}`);
    }
  }
  
  // 检查时间重叠问题
  for (let i = 0; i < parsedData.value.length - 1; i++) {
    const currentEnd = parsedData.value[i].endSeconds;
    const nextStart = parsedData.value[i + 1].startSeconds;
    
    if (currentEnd > nextStart) {
      srtIssues.value.push(`第${i + 1}条和第${i + 2}条字幕的时间有重叠，可能影响显示效果`);
    }
  }
  
  // 检查字幕间隔是否过大
  for (let i = 0; i < parsedData.value.length - 1; i++) {
    const currentEnd = parsedData.value[i].endSeconds;
    const nextStart = parsedData.value[i + 1].startSeconds;
    
    // 如果间隔超过5秒，给出提示
    if (nextStart - currentEnd > 5) {
      srtIssues.value.push(`第${i + 1}条和第${i + 2}条字幕间隔较大(${Math.round(nextStart - currentEnd)}秒)，可能存在遗漏内容`);
    }
  }
  
  // 检查字幕内容长度
  for (let i = 0; i < parsedData.value.length; i++) {
    if (parsedData.value[i].text.length > 100) {
      srtIssues.value.push(`第${i + 1}条字幕内容过长(${parsedData.value[i].text.length}字符)，可能影响阅读体验`);
    }
  }
  
  // 显示检查结果对话框
  issuesDialogVisible.value = true;
  
  // 触发事件
  emit('check-issues', srtIssues.value);
}

// 下载SRT文件
function downloadSrt() {
  if (!parsedData.value.length) {
    ElMessage.warning('没有可下载的SRT内容');
    return;
  }
  
  // 生成SRT内容
  let content = '';
  parsedData.value.forEach((item, index) => {
    content += `${index + 1}\n`;
    content += `${item.startTime} --> ${item.endTime}\n`;
    content += `${item.text}\n\n`;
  });
  
  // 创建下载链接
  const fileName = props.fileName || 'subtitle.srt';
  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  link.click();
  URL.revokeObjectURL(url);
  
  ElMessage.success('SRT文件下载成功');
  
  // 触发事件
  emit('download', fileName);
}

// 暴露方法给父组件
defineExpose({
  parseSrtContent,
  checkSrtIssues,
  downloadSrt,
  toggleFullscreen
});
</script>

<style lang="scss" scoped>
.srt-preview-component {
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
  
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background-color: #ffffff;
    padding: 20px;
    box-sizing: border-box;
    
    .srt-content {
      max-height: calc(100vh - 80px) !important;
      height: calc(100vh - 80px);
    }
  }
  
  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 16px;
    background-color: #ecf5ff;
    border-bottom: 1px solid #d9ecff;
    
    .file-name {
      font-weight: 500;
      color: #606266;
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 300px;
    }
    
    .preview-actions {
      display: flex;
      gap: 8px;
      
      .el-button {
        padding: 6px;
      }
    }
  }
  
  .srt-content {
    overflow-y: auto;
    padding: 16px;
    
    .srt-entries {
      .srt-entry {
        margin-bottom: 16px;
        padding-bottom: 8px;
        
        .srt-index {
          color: #909399;
          font-weight: bold;
          margin-bottom: 4px;
        }
        
        .srt-time {
          color: #409EFF;
          margin-bottom: 4px;
          font-weight: 500;
          
          &.time-discontinuous {
            color: #E6A23C;
            font-weight: bold;
            background-color: rgba(230, 162, 60, 0.1);
            padding: 2px 4px;
            border-radius: 2px;
            display: inline-block;
          }
        }
        
        .srt-text {
          white-space: pre-wrap;
          word-break: break-word;
          margin-bottom: 4px;
          font-family: 'Courier New', Courier, monospace;
          font-size: 14px;
          line-height: 1.5;
        }
        
        .srt-gap-indicator {
          display: flex;
          align-items: center;
          margin-top: 8px;
          margin-bottom: 4px;
          
          .gap-line {
            flex: 1;
            height: 1px;
            background-color: #E6A23C;
          }
          
          .gap-text {
            margin: 0 10px;
            color: #E6A23C;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
          }
        }
      }
    }
    
    .loading-placeholder,
    .empty-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #909399;
      
      .el-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }
    }
    
    .loading-placeholder {
      .loading-icon {
        animation: rotating 2s linear infinite;
      }
    }
  }
  
  .issues-content {
    max-height: 400px;
    overflow-y: auto;
    
    .issues-list {
      margin-top: 16px;
      
      .issue-item {
        display: flex;
        align-items: flex-start;
        padding: 8px 0;
        border-bottom: 1px solid #ebeef5;
        
        &:last-child {
          border-bottom: none;
        }
        
        .issue-tag {
          margin-right: 8px;
          margin-top: 2px;
        }
      }
    }
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 