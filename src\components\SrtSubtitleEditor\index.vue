<template>
  <div class="srt-subtitle-editor">
    <div class="editor-header">
      <div class="file-info" v-if="fileName">
        <div class="file-name-container">
          <span>当前文件: </span>
          <el-input 
            v-model="fileName" 
            size="small" 
            placeholder="文件名" 
            style="width: 200px; margin: 0 10px;"
            @change="validateFileName"
          />
        </div>
        <el-button type="primary" size="small" @click="saveFile">保存文件</el-button>
        <el-button type="warning" size="small" @click="resetEditor">清空</el-button>
        <el-button v-if="showDownload" type="primary" size="small" @click="downloadSrtFile" :disabled="!subtitleData.length">下载文件</el-button>
        <el-button v-if="showUpload" type="success" size="small" @click="uploadToServer">文件上传</el-button>
        <el-button v-if="showParagraph" type="info" size="small" @click="showMergeOptionsDialog" :disabled="selectedSubtitles.length < 2">字幕段落化</el-button>
      </div>
      
      <div v-if="showFileSelector" class="file-upload">
        <el-upload
          class="subtitle-upload"
          action="#"
          :auto-upload="false"
          :show-file-list="false"
          :on-change="handleFileChange"
          accept=".srt"
        >
          <el-button type="primary">选择SRT字幕文件</el-button>
          <template #tip>
            <div class="el-upload__tip" v-if="showTip">
              请选择 <b style="color: #f56c6c">SRT</b> 格式字幕文件
            </div>
          </template>
        </el-upload>
      </div>
    </div>
    
    <div class="editor-body">
      <div class="subtitle-editor">
        <el-table
          :data="subtitleData"
          height="600px"
          style="width: 100%"
          border
          @row-click="handleRowClick"
          @selection-change="handleSelectionChange"
          ref="subtitleTable"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column prop="index" label="序号" width="80" align="center" />
          <el-table-column prop="startTime" label="开始时间" width="120" align="center" />
          <el-table-column prop="endTime" label="结束时间" width="120" align="center" />
          <el-table-column prop="text" label="字幕内容" align="left" :show-overflow-tooltip="true">
            <template #default="scope">
              <div class="subtitle-content">{{ scope.row.text }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" align="center" fixed="right">
            <template #default="scope">
              <div class="operation-buttons">
                <el-button
                  size="small"
                  type="primary"
                  @click.stop="editSubtitle(scope.$index)"
                >
                  编辑
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  @click.stop="deleteSubtitle(scope.$index)"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <div v-if="showTableOperations && subtitleData.length > 0" class="table-operations">
          <div>
            <el-button v-if="showParagraph" type="primary" size="small" @click="showMergeOptionsDialog" :disabled="selectedSubtitles.length < 2">
              合并为段落
            </el-button>
            <el-button v-if="showSplit" type="warning" size="small" @click="splitSubtitle" :disabled="selectedSubtitles.length !== 1">
              拆分字幕
            </el-button>
            <el-tooltip content="选择多条字幕后点击合并，会按时间顺序组合成段落" placement="top">
              <el-button type="info" size="small" plain>
                <el-icon><QuestionFilled /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
          
          <div class="selection-info" v-if="selectedSubtitles.length > 0">
            已选择 {{ selectedSubtitles.length }} 条字幕
          </div>
          
          <div class="table-operations-right">
            <el-button type="success" size="small" @click="selectAllSubtitles">
              全选
            </el-button>
            <el-button type="danger" size="small" @click="clearSelection">
              清除选择
            </el-button>
          </div>
        </div>
      </div>
      
      <div class="subtitle-editor-form" v-if="editing">
        <el-form :model="currentSubtitle" label-width="100px">
          <el-form-item label="开始时间">
            <el-input v-model="currentSubtitle.startTime" placeholder="格式: 00:00:00,000" />
          </el-form-item>
          <el-form-item label="结束时间">
            <el-input v-model="currentSubtitle.endTime" placeholder="格式: 00:00:00,000" />
          </el-form-item>
          <el-form-item label="字幕内容">
            <el-input
              v-model="currentSubtitle.text"
              type="textarea"
              :rows="4"
              placeholder="输入字幕内容"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="saveSubtitle">保存</el-button>
            <el-button @click="cancelEdit">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    
    <el-dialog
      v-model="dialogVisible"
      title="添加新字幕"
      width="50%"
    >
      <el-form :model="newSubtitle" label-width="100px">
        <el-form-item label="开始时间">
          <el-input v-model="newSubtitle.startTime" placeholder="格式: 00:00:00,000" />
        </el-form-item>
        <el-form-item label="结束时间">
          <el-input v-model="newSubtitle.endTime" placeholder="格式: 00:00:00,000" />
        </el-form-item>
        <el-form-item label="字幕内容">
          <el-input
            v-model="newSubtitle.text"
            type="textarea"
            :rows="4"
            placeholder="输入字幕内容"
          />
        </el-form-item>
        <div class="time-sorting-tip">
          <el-alert
            title="提示：字幕会根据开始时间自动排序"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="addNewSubtitle">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 上传到服务器对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="文件上传"
      width="40%"
    >
      <div class="upload-dialog-content">
        <p>确认上传当前编辑的字幕文件？</p>
        <div class="file-info-display">
          <div class="file-name-edit">
            <strong>文件名：</strong>
            <el-input 
              v-model="fileName" 
              size="small" 
              style="width: 250px; margin-left: 10px;" 
              @change="validateFileName" 
            />
          </div>
          <p><strong>字幕条数：</strong>{{ subtitleData.length }}</p>
          <p><strong>总时长：</strong>{{ formatDuration(getTotalDuration()) }}</p>
          <div v-if="validationWarnings.length > 0" class="validation-warnings">
            <p class="warning-title"><strong>检测到以下警告：</strong></p>
            <div class="warnings-list">
              <div v-for="(warning, index) in validationWarnings" :key="index" class="warning-item">
                <el-icon><WarningFilled /></el-icon>
                <span>{{ warning }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmUploadToServer">
            确认上传
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 字幕拆分对话框 -->
    <el-dialog
      v-model="splitDialogVisible"
      title="拆分字幕"
      width="50%"
    >
      <div class="split-dialog-content">
        <p>请在下方输入要拆分的位置（每行一个时间点，格式：00:00:00,000）：</p>
        <el-input
          v-model="splitPoints"
          type="textarea"
          :rows="5"
          placeholder="例如：00:01:30,000"
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="splitDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSplitSubtitle">
            确认拆分
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 字幕合并选项对话框 -->
    <el-dialog
      v-model="mergeOptionsDialogVisible"
      title="字幕段落化"
      width="40%"
    >
      <div class="merge-options-content">
        <p>您选择了 <strong>{{ selectedSubtitles.length }}</strong> 条字幕进行合并，请选择合并方式：</p>
        
        <el-form label-width="120px">
          <el-form-item label="合并方式">
            <el-radio-group v-model="subtitleMergeOptions.joinType">
              <el-radio label="space">使用空格连接</el-radio>
              <el-radio label="newline">使用换行连接</el-radio>
              <el-radio label="paragraph">段落模式（双换行）</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="时间处理">
            <el-radio-group v-model="subtitleMergeOptions.timeHandling">
              <el-radio label="auto">使用首尾时间</el-radio>
              <el-radio label="custom">自定义时间</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <template v-if="subtitleMergeOptions.timeHandling === 'custom'">
            <el-form-item label="开始时间">
              <el-input v-model="subtitleMergeOptions.startTime" placeholder="格式: 00:00:00,000" />
            </el-form-item>
            <el-form-item label="结束时间">
              <el-input v-model="subtitleMergeOptions.endTime" placeholder="格式: 00:00:00,000" />
            </el-form-item>
          </template>
          
          <el-form-item label="预览">
            <div class="merge-preview">
              <pre>{{ getMergePreview() }}</pre>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="mergeOptionsDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="mergeSelectedSubtitlesWithOptions">
            确认合并
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { WarningFilled, QuestionFilled } from '@element-plus/icons-vue'
import { getToken } from "@/utils/auth"

const props = defineProps({
  // 是否显示提示信息
  showTip: {
    type: Boolean,
    default: true
  },
  // 上传接口地址
  action: {
    type: String,
    default: "/common/upload"
  },
  // 初始字幕内容
  initialContent: {
    type: String,
    default: ''
  },
  // 初始文件名
  initialFileName: {
    type: String,
    default: ''
  },
  // 是否显示上传按钮
  showUpload: {
    type: Boolean,
    default: true
  },
  // 是否显示下载按钮
  showDownload: {
    type: Boolean,
    default: true
  },
  // 是否显示段落化按钮
  showParagraph: {
    type: Boolean,
    default: true
  },
  // 是否显示拆分按钮
  showSplit: {
    type: Boolean,
    default: true
  },
  // 是否显示文件选择器
  showFileSelector: {
    type: Boolean,
    default: true
  },
  // 是否显示表格操作区
  showTableOperations: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits([
  'update:content', 
  'file-loaded', 
  'file-uploaded', 
  'subtitle-added',
  'subtitle-edited',
  'subtitle-deleted',
  'subtitle-merged',
  'subtitle-split'
])

// 数据定义
const fileName = ref(props.initialFileName || '')
const subtitleData = ref([])
const srtContent = ref(props.initialContent || '')
const editing = ref(false)
const currentIndex = ref(-1)
const dialogVisible = ref(false)
const uploadDialogVisible = ref(false)
const splitDialogVisible = ref(false)
const validationWarnings = ref([])
const selectedSubtitles = ref([])
const splitPoints = ref('')
const baseUrl = import.meta.env.VITE_APP_BASE_API
const uploadFileUrl = baseUrl + props.action
const headers = { Authorization: "Bearer " + getToken() }

// 初始化组件
if (props.initialContent) {
  parseSrtContent(props.initialContent)
}

// 当前编辑的字幕
const currentSubtitle = ref({
  index: 0,
  startTime: '',
  endTime: '',
  text: ''
})

// 新字幕
const newSubtitle = ref({
  startTime: '',
  endTime: '',
  text: ''
})

// 表格引用
const subtitleTable = ref(null)

const mergeOptionsDialogVisible = ref(false)
const subtitleMergeOptions = ref({
  joinType: 'paragraph', // space, newline, paragraph
  timeHandling: 'auto', // auto, custom
  startTime: '',
  endTime: ''
})

// 方法定义
// 处理文件选择
function handleFileChange(file) {
  if (!file) return
  
  const fileExtension = file.name.split('.').pop().toLowerCase()
  if (fileExtension !== 'srt') {
    ElMessage.error('请选择SRT格式的字幕文件!')
    return
  }
  
  fileName.value = file.name
  
  // 使用FileReader读取文件内容
  const reader = new FileReader()
  reader.onload = (e) => {
    const content = e.target.result
    parseSrtContent(content)
    ElMessage.success('文件已加载')
    emit('file-loaded', file.name)
  }
  
  reader.onerror = () => {
    ElMessage.error('文件读取失败')
  }
  
  reader.readAsText(file.raw)
}

// 解析SRT内容
function parseSrtContent(content) {
  srtContent.value = content
  const lines = content.split('\n')
  const data = []
  
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].trim() === '') continue
    
    const index = lines[i].trim()
    if (!isNaN(index)) {
      i++
      if (i < lines.length) {
        const timeLine = lines[i]
        const timeMatch = timeLine.match(/(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})/)
        if (timeMatch) {
          const startTime = timeMatch[1]
          const endTime = timeMatch[2]
          i++
          let text = ''
          while (i < lines.length && lines[i].trim() !== '') {
            text += lines[i] + '\n'
            i++
          }
          
          data.push({
            index: parseInt(index), // 原始索引，后面会重新排序和编号
            startTime,
            endTime,
            text: text.trim()
          })
        }
      }
    }
  }
  
  // 按开始时间排序字幕
  data.sort((a, b) => {
    return timeStringToSeconds(a.startTime) - timeStringToSeconds(b.startTime)
  })
  
  // 更新排序后的索引
  subtitleData.value = data.map((item, idx) => {
    item.index = idx + 1
    return item
  })
}

// 生成SRT内容
function generateSrtContent() {
  let content = ''
  subtitleData.value.forEach((item, index) => {
    content += `${index + 1}\n`
    content += `${item.startTime} --> ${item.endTime}\n`
    content += `${item.text}\n\n`
  })
  return content
}

// 编辑字幕
function editSubtitle(index) {
  currentIndex.value = index
  currentSubtitle.value = { ...subtitleData.value[index] }
  editing.value = true
}

// 保存编辑的字幕
function saveSubtitle() {
  if (!validateSubtitle(currentSubtitle.value)) return
  
  // 先删除原来的字幕
  subtitleData.value.splice(currentIndex.value, 1)
  
  // 获取编辑后的字幕开始时间
  const editedStartTime = timeStringToSeconds(currentSubtitle.value.startTime)
  
  // 找到应该插入的位置
  let insertIndex = 0
  for (let i = 0; i < subtitleData.value.length; i++) {
    const currentStartTime = timeStringToSeconds(subtitleData.value[i].startTime)
    if (editedStartTime >= currentStartTime) {
      insertIndex = i + 1
    } else {
      break
    }
  }
  
  // 在正确的位置插入编辑后的字幕
  const editedSubtitle = { ...currentSubtitle.value }
  subtitleData.value.splice(insertIndex, 0, editedSubtitle)
  
  // 更新所有字幕的索引
  updateSubtitleIndices()
  
  srtContent.value = generateSrtContent()
  emit('update:content', srtContent.value)
  emit('subtitle-edited', editedSubtitle)
  editing.value = false
}

// 取消编辑
function cancelEdit() {
  editing.value = false
  currentIndex.value = -1
}

// 删除字幕
function deleteSubtitle(index) {
  ElMessageBox.confirm('确定要删除这条字幕吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const deletedSubtitle = { ...subtitleData.value[index] }
    subtitleData.value.splice(index, 1)
    // 更新索引
    updateSubtitleIndices()
    srtContent.value = generateSrtContent()
    emit('update:content', srtContent.value)
    emit('subtitle-deleted', deletedSubtitle)
    ElMessage.success('删除成功')
  }).catch(() => {})
}

// 点击行
function handleRowClick(row) {
  // 可以添加行点击事件，如播放对应时间点的视频等
}

// 添加新字幕对话框
function showAddDialog() {
  dialogVisible.value = true
  newSubtitle.value = {
    startTime: '',
    endTime: '',
    text: ''
  }
}

// 添加新字幕
function addNewSubtitle() {
  if (!validateSubtitle(newSubtitle.value)) return
  
  // 将字幕按照开始时间插入到正确的位置
  const newStartTime = timeStringToSeconds(newSubtitle.value.startTime)
  let insertIndex = 0
  
  // 寻找正确的插入位置
  for (let i = 0; i < subtitleData.value.length; i++) {
    const currentStartTime = timeStringToSeconds(subtitleData.value[i].startTime)
    if (newStartTime >= currentStartTime) {
      insertIndex = i + 1
    } else {
      break
    }
  }
  
  // 在确定的位置插入新字幕
  const newSubtitleItem = {
    index: insertIndex + 1, // 临时索引，后面会更新
    ...newSubtitle.value
  }
  
  subtitleData.value.splice(insertIndex, 0, newSubtitleItem)
  
  // 更新所有字幕的索引
  updateSubtitleIndices()
  
  srtContent.value = generateSrtContent()
  emit('update:content', srtContent.value)
  emit('subtitle-added', newSubtitleItem)
  dialogVisible.value = false
  ElMessage.success('添加成功')
}

// 添加一个更新所有字幕索引的函数
function updateSubtitleIndices() {
  subtitleData.value = subtitleData.value.map((item, idx) => {
    item.index = idx + 1
    return item
  })
}

// 验证字幕格式
function validateSubtitle(subtitle) {
  const timeRegex = /^\d{2}:\d{2}:\d{2},\d{3}$/
  
  // 检查开始时间格式
  if (!timeRegex.test(subtitle.startTime)) {
    ElMessage.error('开始时间格式不正确，应为 00:00:00,000')
    return false
  }
  
  // 检查结束时间格式
  if (!timeRegex.test(subtitle.endTime)) {
    ElMessage.error('结束时间格式不正确，应为 00:00:00,000')
    return false
  }
  
  // 检查字幕内容是否为空
  if (!subtitle.text.trim()) {
    ElMessage.error('字幕内容不能为空')
    return false
  }
  
  // 检查时间有效性
  const startTimeSeconds = timeStringToSeconds(subtitle.startTime)
  const endTimeSeconds = timeStringToSeconds(subtitle.endTime)
  
  if (startTimeSeconds >= endTimeSeconds) {
    ElMessage.error('结束时间必须晚于开始时间')
    return false
  }
  
  // 检查字幕内容长度
  if (subtitle.text.length > 300) {
    ElMessage.warning('字幕内容过长，可能影响阅读体验，建议分段处理')
  }
  
  return true
}

// 字符串时间格式转换为秒数
function timeStringToSeconds(timeStr) {
  const [time, milliseconds] = timeStr.split(',')
  const [hours, minutes, seconds] = time.split(':').map(Number)
  return hours * 3600 + minutes * 60 + seconds + Number(milliseconds) / 1000
}

// 秒数转换为时间字符串
function secondsToTimeString(seconds) {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  const milliseconds = Math.floor((seconds % 1) * 1000)
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`
}

// 检查SRT文件内容的完整性
function checkSrtContent() {
  const warnings = []
  
  // 检查字幕序号是否连续
  for (let i = 0; i < subtitleData.value.length; i++) {
    if (subtitleData.value[i].index !== i + 1) {
      warnings.push(`字幕序号不连续，第${i + 1}条字幕序号为${subtitleData.value[i].index}`)
    }
  }
  
  // 检查时间重叠问题
  for (let i = 0; i < subtitleData.value.length - 1; i++) {
    const currentEnd = timeStringToSeconds(subtitleData.value[i].endTime)
    const nextStart = timeStringToSeconds(subtitleData.value[i + 1].startTime)
    
    if (currentEnd > nextStart) {
      warnings.push(`第${i + 1}条和第${i + 2}条字幕的时间有重叠，可能影响显示效果`)
    }
  }
  
  // 检查字幕间隔是否过大
  for (let i = 0; i < subtitleData.value.length - 1; i++) {
    const currentEnd = timeStringToSeconds(subtitleData.value[i].endTime)
    const nextStart = timeStringToSeconds(subtitleData.value[i + 1].startTime)
    
    // 如果间隔超过5秒，给出提示
    if (nextStart - currentEnd > 5) {
      warnings.push(`第${i + 1}条和第${i + 2}条字幕间隔较大(${Math.round(nextStart - currentEnd)}秒)，可能存在遗漏内容`)
    }
  }
  
  return warnings
}

// 文件名验证函数
function validateFileName() {
  if (!fileName.value) {
    ElMessage.warning('文件名不能为空')
    fileName.value = 'subtitle.srt'
    return false
  }
  
  // 确保有.srt后缀
  if (!fileName.value.toLowerCase().endsWith('.srt')) {
    fileName.value = fileName.value + '.srt'
  }
  
  // 检查文件名是否含有非法字符
  const invalidChars = /[\\/:*?"<>|]/g
  if (invalidChars.test(fileName.value)) {
    const cleanFileName = fileName.value.replace(invalidChars, '_')
    ElMessage.warning(`文件名包含非法字符，已自动替换为: ${cleanFileName}`)
    fileName.value = cleanFileName
  }
  
  return true
}

// 保存文件
function saveFile() {
  const content = generateSrtContent()
  srtContent.value = content
  emit('update:content', content)
  ElMessage.success('保存成功')
}

// 重置编辑器
function resetEditor() {
  ElMessageBox.confirm('确定要清空当前内容吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    fileName.value = ''
    subtitleData.value = []
    srtContent.value = ''
    editing.value = false
    currentIndex.value = -1
    emit('update:content', '')
    ElMessage.success('已清空')
  }).catch(() => {})
}

// 下载SRT文件
function downloadSrtFile() {
  if (subtitleData.value.length === 0) {
    ElMessage.warning('没有可下载的字幕内容')
    return
  }

  validateFileName()
  
  const content = generateSrtContent()
  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = fileName.value
  link.click()
  URL.revokeObjectURL(url)
  ElMessage.success('文件下载成功')
}

// 获取字幕总时长（秒）
function getTotalDuration() {
  if (subtitleData.value.length === 0) return 0;
  
  const lastSubtitle = subtitleData.value[subtitleData.value.length - 1];
  return timeStringToSeconds(lastSubtitle.endTime);
}

// 格式化时长显示
function formatDuration(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  let result = '';
  if (hours > 0) {
    result += `${hours}小时 `;
  }
  if (minutes > 0 || hours > 0) {
    result += `${minutes}分钟 `;
  }
  result += `${secs}秒`;
  
  return result;
}

// 上传前校验
function preUploadValidation() {
  // 验证文件名
  if (!validateFileName()) {
    return false
  }
  
  // 检查字幕内容是否为空
  if (subtitleData.value.length === 0) {
    ElMessage.error('字幕内容不能为空')
    return false
  }
  
  // 检查每条字幕内容
  for (let i = 0; i < subtitleData.value.length; i++) {
    const subtitle = subtitleData.value[i]
    
    // 检查时间格式
    const timeRegex = /^\d{2}:\d{2}:\d{2},\d{3}$/
    if (!timeRegex.test(subtitle.startTime) || !timeRegex.test(subtitle.endTime)) {
      ElMessage.error(`第${i + 1}条字幕时间格式错误，请检查`)
      return false
    }
    
    // 检查结束时间是否大于开始时间
    const startTime = timeStringToSeconds(subtitle.startTime)
    const endTime = timeStringToSeconds(subtitle.endTime)
    if (startTime >= endTime) {
      ElMessage.error(`第${i + 1}条字幕的结束时间必须大于开始时间`)
      return false
    }
    
    // 检查字幕文本是否为空
    if (!subtitle.text.trim()) {
      ElMessage.error(`第${i + 1}条字幕内容为空`)
      return false
    }
  }
  
  return true
}

// 显示上传对话框
function uploadToServer() {
  if (subtitleData.value.length === 0) {
    ElMessage.warning('没有可上传的字幕内容')
    return
  }
  
  // 上传前进行校验
  if (preUploadValidation()) {
    validationWarnings.value = checkSrtContent()
    uploadDialogVisible.value = true
  }
}

// 确认上传到服务器
function confirmUploadToServer() {
  // 再次校验文件名
  if (!validateFileName()) {
    return
  }
  
  // 准备上传数据
  const content = generateSrtContent()
  const formData = new FormData()
  const blob = new Blob([content], { type: 'text/plain' })
  const file = new File([blob], fileName.value, { type: 'text/plain' })
  
  formData.append('file', file)
  
  // 显示上传中提示
  const loading = ElMessage.loading({
    message: '正在上传中...',
    duration: 0
  })
  
  // 发送上传请求
  fetch(uploadFileUrl, {
    method: 'POST',
    body: formData,
    headers: headers
  })
    .then(response => response.json())
    .then(result => {
      loading.close()
      if (result.code === 200) {
        ElMessage.success('上传成功')
        emit('file-uploaded', result)
        uploadDialogVisible.value = false
      } else {
        ElMessage.error(result.msg || '上传失败')
      }
    })
    .catch(error => {
      loading.close()
      ElMessage.error('上传过程中出现错误')
      console.error('Upload error:', error)
    })
}

// 表格选择变化
function handleSelectionChange(selection) {
  selectedSubtitles.value = selection
}

// 显示合并选项对话框
function showMergeOptionsDialog() {
  if (selectedSubtitles.value.length < 2) {
    ElMessage.warning('请至少选择两条字幕进行合并')
    return
  }
  
  // 重置合并选项
  subtitleMergeOptions.value = {
    joinType: 'paragraph',
    timeHandling: 'auto',
    startTime: '',
    endTime: ''
  }
  
  // 如果是自定义时间，预设为第一个和最后一个字幕的时间
  const selectedItems = [...selectedSubtitles.value].sort((a, b) => {
    return timeStringToSeconds(a.startTime) - timeStringToSeconds(b.startTime)
  })
  
  if (selectedItems.length >= 2) {
    subtitleMergeOptions.value.startTime = selectedItems[0].startTime
    subtitleMergeOptions.value.endTime = selectedItems[selectedItems.length - 1].endTime
  }
  
  mergeOptionsDialogVisible.value = true
}

// 获取合并预览
function getMergePreview() {
  if (selectedSubtitles.value.length < 2) return ''
  
  // 按时间排序
  const selectedItems = [...selectedSubtitles.value].sort((a, b) => {
    return timeStringToSeconds(a.startTime) - timeStringToSeconds(b.startTime)
  })
  
  let joinChar = ' '
  switch (subtitleMergeOptions.value.joinType) {
    case 'space':
      joinChar = ' '
      break
    case 'newline':
      joinChar = '\n'
      break
    case 'paragraph':
      joinChar = '\n\n'
      break
  }
  
  // 合并文本
  return selectedItems.map(item => item.text).join(joinChar)
}

// 使用选项合并字幕
function mergeSelectedSubtitlesWithOptions() {
  if (selectedSubtitles.value.length < 2) {
    ElMessage.warning('请至少选择两条字幕进行合并')
    return
  }
  
  // 验证自定义时间
  if (subtitleMergeOptions.value.timeHandling === 'custom') {
    const timeRegex = /^\d{2}:\d{2}:\d{2},\d{3}$/
    if (!timeRegex.test(subtitleMergeOptions.value.startTime) || 
        !timeRegex.test(subtitleMergeOptions.value.endTime)) {
      ElMessage.error('时间格式不正确，应为 00:00:00,000')
      return
    }
    
    const startTime = timeStringToSeconds(subtitleMergeOptions.value.startTime)
    const endTime = timeStringToSeconds(subtitleMergeOptions.value.endTime)
    
    if (startTime >= endTime) {
      ElMessage.error('结束时间必须晚于开始时间')
      return
    }
  }
  
  // 获取选中的字幕，并按时间顺序排序
  const selectedItems = [...selectedSubtitles.value].sort((a, b) => {
    return timeStringToSeconds(a.startTime) - timeStringToSeconds(b.startTime)
  })
  
  // 确定开始和结束时间
  let startTime, endTime
  if (subtitleMergeOptions.value.timeHandling === 'custom') {
    startTime = subtitleMergeOptions.value.startTime
    endTime = subtitleMergeOptions.value.endTime
  } else {
    startTime = selectedItems[0].startTime
    endTime = selectedItems[selectedItems.length - 1].endTime
  }
  
  // 确定连接字符
  let joinChar = ' '
  switch (subtitleMergeOptions.value.joinType) {
    case 'space':
      joinChar = ' '
      break
    case 'newline':
      joinChar = '\n'
      break
    case 'paragraph':
      joinChar = '\n\n'
      break
  }
  
  // 合并文本
  const mergedText = selectedItems.map(item => item.text).join(joinChar)
  
  // 创建合并后的字幕
  const mergedSubtitle = {
    index: selectedItems[0].index,
    startTime: startTime,
    endTime: endTime,
    text: mergedText
  }
  
  // 获取所有选中字幕的索引
  const indices = selectedItems.map(item => subtitleData.value.findIndex(s => s.index === item.index))
  
  // 从后往前删除，避免索引变化
  indices.sort((a, b) => b - a)
  for (let i = 0; i < indices.length; i++) {
    subtitleData.value.splice(indices[i], 1)
  }
  
  // 找到应该插入的位置
  const insertTime = timeStringToSeconds(mergedSubtitle.startTime)
  let insertIndex = 0
  for (let i = 0; i < subtitleData.value.length; i++) {
    const currentTime = timeStringToSeconds(subtitleData.value[i].startTime)
    if (insertTime >= currentTime) {
      insertIndex = i + 1
    } else {
      break
    }
  }
  
  // 插入合并后的字幕
  subtitleData.value.splice(insertIndex, 0, mergedSubtitle)
  
  // 更新索引
  updateSubtitleIndices()
  
  // 更新内容
  srtContent.value = generateSrtContent()
  emit('update:content', srtContent.value)
  emit('subtitle-merged', {
    mergedSubtitle,
    originalSubtitles: selectedItems
  })
  
  mergeOptionsDialogVisible.value = false
  ElMessage.success('字幕合并成功')
}

// 显示拆分对话框
function splitSubtitle() {
  if (selectedSubtitles.value.length !== 1) {
    ElMessage.warning('请选择一条字幕进行拆分')
    return
  }
  
  splitDialogVisible.value = true
  splitPoints.value = ''
}

// 确认拆分字幕
function confirmSplitSubtitle() {
  if (!splitPoints.value.trim()) {
    ElMessage.warning('请输入拆分时间点')
    return
  }
  
  const timePoints = splitPoints.value.split('\n')
    .map(point => point.trim())
    .filter(point => point !== '')
  
  // 验证时间格式
  const timeRegex = /^\d{2}:\d{2}:\d{2},\d{3}$/
  for (const point of timePoints) {
    if (!timeRegex.test(point)) {
      ElMessage.error(`时间点 ${point} 格式不正确，应为 00:00:00,000`)
      return
    }
  }
  
  // 获取选中的字幕
  const subtitle = selectedSubtitles.value[0]
  const subtitleIndex = subtitleData.value.findIndex(s => s.index === subtitle.index)
  
  // 验证时间点在字幕时间范围内
  const startSeconds = timeStringToSeconds(subtitle.startTime)
  const endSeconds = timeStringToSeconds(subtitle.endTime)
  
  const validTimePoints = timePoints
    .map(point => timeStringToSeconds(point))
    .filter(seconds => seconds > startSeconds && seconds < endSeconds)
    .sort((a, b) => a - b)
  
  if (validTimePoints.length === 0) {
    ElMessage.warning('没有有效的拆分时间点')
    return
  }
  
  // 创建拆分后的字幕
  const splitSubtitles = []
  
  // 第一段
  splitSubtitles.push({
    index: subtitle.index,
    startTime: subtitle.startTime,
    endTime: secondsToTimeString(validTimePoints[0]),
    text: subtitle.text
  })
  
  // 中间段
  for (let i = 0; i < validTimePoints.length - 1; i++) {
    splitSubtitles.push({
      index: subtitle.index,
      startTime: secondsToTimeString(validTimePoints[i]),
      endTime: secondsToTimeString(validTimePoints[i + 1]),
      text: subtitle.text
    })
  }
  
  // 最后一段
  splitSubtitles.push({
    index: subtitle.index,
    startTime: secondsToTimeString(validTimePoints[validTimePoints.length - 1]),
    endTime: subtitle.endTime,
    text: subtitle.text
  })
  
  // 删除原字幕
  subtitleData.value.splice(subtitleIndex, 1)
  
  // 插入拆分后的字幕
  for (let i = 0; i < splitSubtitles.length; i++) {
    subtitleData.value.splice(subtitleIndex + i, 0, splitSubtitles[i])
  }
  
  // 更新索引
  updateSubtitleIndices()
  
  // 更新内容
  srtContent.value = generateSrtContent()
  emit('update:content', srtContent.value)
  emit('subtitle-split', {
    originalSubtitle: subtitle,
    splitSubtitles: splitSubtitles
  })
  
  splitDialogVisible.value = false
  ElMessage.success('字幕拆分成功')
}

// 全选字幕
function selectAllSubtitles() {
  subtitleData.value.forEach(row => {
    nextTick(() => {
      subtitleTable.value.toggleRowSelection(row, true)
    })
  })
}

// 清除选择
function clearSelection() {
  subtitleTable.value.clearSelection()
}

// 获取当前字幕数据
function getSubtitleData() {
  return subtitleData.value
}

// 获取当前SRT内容
function getSrtContent() {
  return srtContent.value
}

// 获取当前文件名
function getFileName() {
  return fileName.value
}

// 设置文件名
function setFileName(name) {
  fileName.value = name
  validateFileName()
  return fileName.value
}

// 清空编辑器
function clearEditor() {
  subtitleData.value = []
  srtContent.value = ''
  fileName.value = ''
  editing.value = false
  currentIndex.value = -1
  emit('update:content', '')
}

// 暴露方法给父组件
defineExpose({
  addSubtitle: showAddDialog,
  resetEditor,
  parseSrtContent,
  downloadSrtFile,
  uploadToServer,
  validateSrtContent: preUploadValidation,
  mergeSubtitles: showMergeOptionsDialog,
  splitSubtitle,
  getSubtitleData,
  getSrtContent,
  getFileName,
  setFileName,
  clearEditor
})
</script>

<style scoped>
.srt-subtitle-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.file-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.file-name-container {
  display: flex;
  align-items: center;
}

.editor-body {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 16px;
}

.subtitle-editor {
  flex: 1;
}

.subtitle-editor-form {
  margin: 16px 0;
  padding: 16px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.subtitle-upload {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.upload-dialog-content {
  padding: 10px;
}

.file-info-display {
  margin-top: 15px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.file-name-edit {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.validation-warnings {
  margin-top: 15px;
  padding: 10px;
  background-color: #fdf6ec;
  border-radius: 4px;
  border-left: 3px solid #e6a23c;
}

.warning-title {
  color: #e6a23c;
  margin-bottom: 10px;
}

.warnings-list {
  max-height: 150px;
  overflow-y: auto;
}

.warning-item {
  display: flex;
  align-items: flex-start;
  gap: 5px;
  margin-bottom: 5px;
  color: #7d6133;
}

.time-sorting-tip {
  margin-top: 15px;
  margin-bottom: 10px;
}

.subtitle-content {
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  text-align: left;
  padding: 5px 0;
}

.operation-buttons {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

.operation-buttons .el-button {
  margin-left: 0;
  padding: 4px 8px;
  min-height: 28px;
  width: 100%;
}

.table-operations {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.table-operations-right {
  display: flex;
  gap: 10px;
}

.selection-info {
  font-size: 14px;
  color: #606266;
  padding: 0 10px;
}

.merge-options-content {
  padding: 10px;
}

.merge-preview {
  max-height: 150px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
}

.split-dialog-content {
  padding: 10px;
}
</style> 