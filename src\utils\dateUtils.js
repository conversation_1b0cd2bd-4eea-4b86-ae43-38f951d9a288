/**
 * 日期处理工具函数
 */

/**
 * 添加日期范围参数，专用于音频和电台查询
 * @param {Object} params 查询参数对象
 * @param {Array} dateRange 日期范围数组 [开始日期, 结束日期]
 * @returns {Object} 添加了日期范围参数的查询参数对象
 */
export function addQueryDateRange(params, dateRange) {
  if (!dateRange || !Array.isArray(dateRange) || dateRange.length !== 2) {
    return params;
  }

  // 创建参数的副本，避免修改原对象
  const result = { ...params };
  
  // 处理日期格式，确保包含时间部分
  const formatDateWithTime = (dateStr) => {
    if (!dateStr) return null;
    // 如果只有日期部分，添加时间部分
    if (dateStr.length === 10) { // YYYY-MM-DD 格式
      return dateStr + ' 00:00:00';
    }
    return dateStr;
  };

  // 处理结束日期，确保是当天的结束时间
  const formatEndDateWithTime = (dateStr) => {
    if (!dateStr) return null;
    // 如果只有日期部分，添加结束时间部分
    if (dateStr.length === 10) { // YYYY-MM-DD 格式
      return dateStr + ' 23:59:59';
    }
    return dateStr;
  };

  // 添加开始和结束时间参数
  result.beginTime = formatDateWithTime(dateRange[0]);
  result.endTime = formatEndDateWithTime(dateRange[1]);
  
  return result;
}

/**
 * 格式化日期范围选择器的值
 * @param {String} dateStr 日期字符串
 * @param {Boolean} isEndDate 是否为结束日期
 * @returns {String} 格式化后的日期字符串
 */
export function formatDateTimeForQuery(dateStr, isEndDate = false) {
  if (!dateStr) return null;
  
  // 如果只有日期部分，添加时间部分
  if (dateStr.length === 10) { // YYYY-MM-DD 格式
    return dateStr + (isEndDate ? ' 23:59:59' : ' 00:00:00');
  }
  return dateStr;
} 