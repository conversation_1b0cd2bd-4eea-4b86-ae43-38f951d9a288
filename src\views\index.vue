<template>
  <div class="dashboard-container">
    <!-- 欢迎图片 -->
    <div class="welcome-image"></div>

    <!-- 欢迎文本 -->
    <div class="welcome-text">👏欢迎回来，{{userStore.nickName}}</div>
  </div>
</template>






<script setup name="Index">
import useUserStore from '@/store/modules/user'
const userStore = useUserStore()

</script>

<style scoped>
.dashboard-container {
  position: relative;
  width: 100%;
  height: 100vh;
  justify-content: center;
  align-items: center;
}

.welcome-image {
  position: absolute;
  left: 554px;
  top: 222px;
  width: 471px;
  height: 471px;
  background-color: rgb(255, 255, 255);
  background-image: url('@/assets/images/undraw_welcome_cats_thqn.png');
  background-size: cover;
  background-position: center;
  text-align: center;
}

.welcome-text {
  position: absolute;
  left: 501px;
  top: 222px;
  width: 548px;
  height: 68px;
  line-height: 68px;
  color: rgb(16, 16, 16);
  font-size: 45px;
  text-align: left;
  font-family: 'Courier New', Courier, monospace;
  text-align: center;
}
</style>


