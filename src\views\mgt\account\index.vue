<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户ID" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入用户ID"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户昵称" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入用户昵称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号码"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="用户状态"
          clearable
          style="width: 200px"
        >
          <el-option label="全部" value="" />
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 300px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['operate:user:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleBatchDelete"
          v-hasPermi="['operate:user:remove']"
        >批量删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="用户ID" align="center" prop="id" width="200" />
      <el-table-column label="用户昵称" prop="nickname" :show-overflow-tooltip="true" width="180" />
      <el-table-column label="手机号码" align="center" prop="phone" width="180" />
      <el-table-column label="头像" align="center" width="120">
        <template #default="scope">
          <el-avatar 
            v-if="scope.row.avatarUrl" 
            :size="35" 
            :src="scope.row.avatarUrl" 
          />
          <el-avatar v-else :size="35" icon="UserFilled" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="120">
        <template #default="scope">
          <dict-tag :options="[
            { label: '启用', value: 1, elTagType: 'success' },
            { label: '禁用', value: 0, elTagType: 'danger' }
          ]" :value="Number(scope.row.status)" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="created" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.created) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Check"
            v-if="scope.row.status === 0"
            @click="handleStatusChange(scope.row, 1)"
            v-hasPermi="['operate:user:edit']"
          >启用</el-button>
          <el-button
            link
            type="primary"
            icon="Close"
            v-if="scope.row.status === 1"
            @click="handleStatusChange(scope.row, 0)"
            v-hasPermi="['operate:user:edit']"
          >禁用</el-button>
          <el-button 
            link 
            type="primary" 
            icon="Delete" 
            @click="handleDelete(scope.row)" 
            v-hasPermi="['operate:user:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form :model="form" :rules="rules" ref="userFormRef" label-width="80px">
        <el-form-item label="用户昵称" prop="nickname">
          <el-input v-model="form.nickname" placeholder="请输入用户昵称" />
        </el-form-item>
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号码" maxlength="11" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!form.id">
          <el-input v-model="form.password" placeholder="请输入密码" type="password" show-password />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="UserMgt">
import { 
  listUser, 
  getUser, 
  addUser, 
  delUser,
  delUserBatch, 
  changeUserStatus
} from "@/api/listen/userMgt";
import { onMounted } from 'vue';
import { parseTime } from '@/utils/ruoyi';
import { addQueryDateRange } from '@/utils/dateUtils';
import DictTag from '@/components/DictTag/index.vue';

const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 用户表格数据
const userList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 日期范围
const dateRange = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  id: undefined,
  nickname: undefined,
  phone: undefined,
  status: undefined
});

// 表单参数
const form = ref({
  id: undefined,
  nickname: undefined,
  phone: undefined,
  password: undefined,
  status: 1
});

// 表单校验
const rules = ref({
  nickname: [
    { required: true, message: "用户昵称不能为空", trigger: "blur" }
  ],
  phone: [
    { required: true, message: "手机号码不能为空", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
  ],
  password: [
    { required: true, message: "密码不能为空", trigger: "blur" },
    { min: 6, message: "密码长度不能小于6位", trigger: "blur" }
  ]
});

/** 查询用户列表 */
function getList() {
  loading.value = true;
  
  // 使用日期工具函数添加日期范围参数
  const finalParams = addQueryDateRange(queryParams.value, dateRange.value);
  
  listUser(finalParams).then(response => {
    userList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加用户";
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    nickname: undefined,
    phone: undefined,
    password: undefined,
    status: 1
  };
  proxy.resetForm("userFormRef");
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["userFormRef"].validate(valid => {
    if (valid) {
      // 确保状态为数字类型
      if (form.value.status !== undefined && form.value.status !== null) {
        form.value.status = Number(form.value.status);
      }
      
      addUser(form.value).then(response => {
        proxy.$modal.msgSuccess("新增成功");
        open.value = false;
        getList();
      });
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const userId = String(row.id);
  proxy.$modal.confirm('是否确认删除用户编号为"' + userId + '"的数据项?').then(function() {
    return delUser(userId);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 批量删除按钮操作 */
function handleBatchDelete() {
  if (ids.value.length === 0) {
    proxy.$modal.msgWarning("请至少选择一条记录");
    return;
  }
  
  // 确保所有ID都是字符串类型
  const userIds = ids.value.map(id => String(id));
  
  proxy.$modal.confirm('是否确认批量删除选中的' + userIds.length + '条数据项?').then(function() {
    return delUserBatch(userIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("批量删除成功");
  }).catch(() => {});
}

/** 用户状态修改 */
function handleStatusChange(row, status) {
  const text = status === 1 ? "启用" : "禁用";
  proxy.$modal.confirm('确认要"' + text + '""' + row.nickname + '"用户吗?').then(function() {
    return changeUserStatus(row.id, status);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(function() {
    row.status = row.status === 1 ? 0 : 1;
  });
}

// 组件挂载时执行
onMounted(() => {
  getList();
});
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}

.small-padding {
  padding-left: 5px;
  padding-right: 5px;
}

.fixed-width {
  min-width: 120px;
}
</style> 