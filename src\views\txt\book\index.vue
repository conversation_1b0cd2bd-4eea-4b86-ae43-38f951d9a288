<template>
  <div class="app-container">
    <div class="search-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="query-form">
        <el-form-item label="书籍编号" prop="id">
          <el-input
            v-model="queryParams.id"
            placeholder="请输入书籍编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="书籍名称" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入书籍名称"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="书籍分级" prop="level">
          <el-select
            v-model="queryParams.level"
            placeholder="请选择书籍分级"
            clearable
            style="width: 200px"
          >
            <el-option label="全部" value="" />
            <el-option label="初级" :value="1" />
            <el-option label="中级" :value="2" />
            <el-option label="高级" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="书籍分类" prop="categoryId">
          <el-select
            v-model="queryParams.categoryId"
            placeholder="请选择书籍分类"
            clearable
            style="width: 200px"
          >
            <el-option label="全部" value="" />
            <el-option
              v-for="category in categoryOptions"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="书籍状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            style="width: 200px"
          >
            <el-option label="全部" value="" />
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" style="width: 300px">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>

    <div class="action-container">
      <div class="button-group">
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </div>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:books:add']"
        >新增</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="bookList" @selection-change="handleSelectionChange" style="width: 100%">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="书籍编号" align="center" prop="id" width="180">
        <template #default="scope">
          <div class="common-cell">{{ scope.row.id }}</div>
        </template>
      </el-table-column>
      <el-table-column label="书籍名称" align="center" prop="name" :show-overflow-tooltip="true" min-width="120">
        <template #default="scope">
          <div class="common-cell">
            <span class="book-name-cell" :title="scope.row.name">{{ scope.row.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="语种" align="center" width="100">
        <template #default="scope">
          <span>{{ scope.row.languageNamesString || '未知' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="简介" align="center" prop="description" min-width="150">
        <template #default="scope">
          <div class="common-cell">
            <div class="description-cell" @click="showDescriptionDialog(scope.row)">
              {{ scope.row.description }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="封面图" align="center" width="100">
        <template #default="scope">
          <div v-if="scope.row.coverUrl" class="image-container" @click="previewBookImages(scope.row, 'cover')">
            <el-image
              :src="scope.row.coverUrl"
              class="table-image cover-image-preview"
              fit="cover"
            >
              <template #placeholder>
                <div class="image-placeholder">
                  <el-icon><Loading /></el-icon>
                  <span>加载中</span>
                </div>
              </template>
              <template #error>
                <div class="image-error">
                  <el-icon><PictureFilled /></el-icon>
                  <span>加载失败</span>
                </div>
              </template>
            </el-image>
            <div class="image-hover-text">
              <el-icon style="margin-right: 4px;"><ZoomIn /></el-icon>
              点击预览
            </div>
          </div>
          <span v-else>无封面</span>
        </template>
      </el-table-column>
      <el-table-column label="书籍分级" align="center" width="100">
        <template #default="scope">
          <dict-tag :options="[
            { label: '初级', value: 1, elTagType: 'primary' },
            { label: '中级', value: 2, elTagType: 'success' },
            { label: '高级', value: 3, elTagType: 'danger' }
          ]" :value="scope.row.level" />
        </template>
      </el-table-column>
      <el-table-column label="书籍分类" align="center" width="100">
        <template #default="scope">
          <span>{{ scope.row.categoryName || '未分类' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="书籍状态" align="center" width="100">
        <template #default="scope">
          <dict-tag :options="[
            { label: '启用', value: 1, elTagType: 'success' },
            { label: '禁用', value: 0, elTagType: 'danger' }
          ]" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="音频数量" align="center" prop="audioCount" width="80" />
      <el-table-column label="书籍播放量" align="center" prop="viewCount" width="100" />
      <el-table-column label="创建时间" align="center" prop="created" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.created) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updated" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updated) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="220" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-tooltip content="修改" placement="top">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:books:edit']"></el-button>
          </el-tooltip>
          <el-tooltip :content="scope.row.status === 0 ? '启用' : '禁用'" placement="top">
            <el-button
              link
              type="primary"
              :icon="scope.row.status === 0 ? 'Check' : 'Close'"
              v-if="scope.row.status === 0 || scope.row.status === 1"
              @click="handleStatusChange(scope.row, scope.row.status === 0 ? 1 : 0)"
              v-hasPermi="['system:books:edit']"
            ></el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:books:remove']"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加/修改书籍对话框 -->
    <el-dialog :title="title" v-model="open" width="550px" append-to-body>
      <el-form ref="bookFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="书籍名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入书籍名称" />
        </el-form-item>
        <el-form-item label="简介" prop="description">
          <el-input v-model="form.description" placeholder="请输入简介" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="上传封面" prop="coverFile">
          <div class="upload-wrapper">
            <el-upload
              class="cover-uploader"
              action="#"
              :http-request="uploadCoverRequest"
              :show-file-list="false"
              :before-upload="beforeCoverUpload"
            >
              <img v-if="form.coverUrl" :src="form.coverUrl" class="cover-image" />
              <div v-else class="cover-upload-placeholder">
                <el-icon><Plus /></el-icon>
                <div>上传封面</div>
              </div>
            </el-upload>
            <div class="upload-tip">只支持 .jpg 格式</div>
          </div>
        </el-form-item>
        <el-form-item label="语种" prop="languageCodes">
          <el-select v-model="form.languageCodes" multiple placeholder="请选择语种" style="width: 100%">
            <el-option 
              v-for="lang in languageOptions"
              :key="lang.code"
              :label="lang.name"
              :value="lang.code"
            />
          </el-select>
          <div class="language-preview" v-if="form.languageCodes && form.languageCodes.length > 0">
            <span>当前选择: </span>
            <el-tag 
              v-for="code in form.languageCodes" 
              :key="code" 
              size="small" 
              effect="light"
              style="margin-right: 5px; margin-top: 5px;"
            >{{ getLanguageName(code) }}</el-tag>
          </div>
        </el-form-item>
        <el-form-item label="书籍分级" prop="level">
          <el-select v-model="form.level" placeholder="请选择书籍分级" style="width: 100%">
            <el-option label="初级" :value="1" />
            <el-option label="中级" :value="2" />
            <el-option label="高级" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="书籍分类" prop="categoryId">
          <el-select v-model="form.categoryId" placeholder="请选择书籍分类" style="width: 100%">
            <el-option
              v-for="category in categoryOptions"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="作者" prop="author">
          <el-input v-model="form.author" placeholder="请输入作者" />
        </el-form-item>
        <el-form-item label="译者" prop="translator">
          <el-input v-model="form.translator" placeholder="请输入译者" />
        </el-form-item>
        <el-form-item label="出版社" prop="publisher">
          <el-input v-model="form.publisher" placeholder="请输入出版社" />
        </el-form-item>
        <el-form-item label="书籍状态" prop="status">
          <el-radio-group v-model="form.status" class="radio-group-full-width">
            <el-radio :value="1">启用</el-radio>
            <el-radio :value="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加图片预览组件 -->
    <el-image-viewer
      v-if="previewVisible"
      :url-list="previewImages"
      :initial-index="previewIndex"
      :z-index="3000"
      :hide-on-click-modal="false"
      :teleported="true"
      :close-on-press-escape="true"
      @close="previewVisible = false"
    >
      <template #viewer-title>
        <div class="image-preview-title">
          封面图
        </div>
      </template>
    </el-image-viewer>

    <!-- 简介详情对话框 -->
    <el-dialog
      v-model="descriptionDialogVisible"
      :title="currentDescription.name"
      width="500px"
      center
      append-to-body
      destroy-on-close
    >
      <div class="description-dialog-content">
        <div class="description-dialog-text">{{ currentDescription.description }}</div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="book">
import { listBooks, getBook, delBook, addBook, updateBook, changeBookStatus, uploadBookCover, listBookCategories, listLanguages } from "@/api/txt/book";
import { parseTime } from '@/utils/ruoyi';
import { addQueryDateRange } from '@/utils/dateUtils';
import DictTag from '@/components/DictTag/index.vue';
import { onMounted, nextTick } from 'vue';
import { Loading, PictureFilled, Plus, ZoomIn } from '@element-plus/icons-vue';
import { ElImageViewer } from 'element-plus';

const { proxy } = getCurrentInstance();

const bookList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref([]);
const title = ref("");
const open = ref(false);
const descriptionDialogVisible = ref(false);
const currentDescription = ref({
  name: "",
  description: ""
});

// 分类和语言选项
const categoryOptions = ref([]);
const languageOptions = ref([
  { code: 1, name: "中文" },    // 二进制 0001
  { code: 2, name: "俄语" },    // 二进制 0010
  { code: 4, name: "法语" },    // 二进制 0100
  { code: 8, name: "英语" },    // 二进制 1000
  { code: 16, name: "西班牙语" } // 二进制 10000
]);

const data = reactive({
  form: {
    id: undefined,
    name: '',
    description: '',
    coverUrl: '',
    coverFileId: undefined,
    coverFile: undefined,
    languageCodes: [],
    level: 1,
    categoryId: undefined,
    author: '',
    translator: '',
    publisher: '',
    status: 0
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    id: undefined,
    name: undefined,
    level: undefined,
    categoryId: undefined,
    status: undefined
  },
  rules: {
    name: [
      { required: true, message: "书籍名称不能为空", trigger: "blur" }
    ],
    description: [
      { required: true, message: "简介不能为空", trigger: "blur" }
    ],
    languageCodes: [
      { required: true, message: "语种不能为空", trigger: "change" }
    ],
    level: [
      { required: true, message: "书籍分级不能为空", trigger: "change" }
    ],
    categoryId: [
      { required: true, message: "书籍分类不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 图片预览相关
const previewImages = ref([]);
const previewVisible = ref(false);
const previewIndex = ref(0);

/** 查询书籍列表 */
function getList() {
  loading.value = true;
  
  // 使用日期工具函数添加日期范围参数
  const finalParams = addQueryDateRange(queryParams.value, dateRange.value);
  
  listBooks(finalParams).then(response => {
    bookList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(error => {
    loading.value = false;
  });
}

/** 预览书籍图片 */
function previewBookImages(row, imageType) {
  // 收集所有有效的图片URL
  const images = [];
  
  // 添加封面图
  if (row.coverUrl) {
    images.push(row.coverUrl);
  }
  
  // 如果有图片，显示预览
  if (images.length > 0) {
    // 先设置图片数组，再设置索引，最后显示预览
    previewImages.value = images;
    previewIndex.value = 0;
    
    // 使用nextTick确保DOM已更新
    nextTick(() => {
      previewVisible.value = true;
    });
  } else {
    // 没有图片时提示用户
    proxy.$modal.msgWarning("没有可预览的图片");
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  
  // 确保状态为数字类型
  if (queryParams.value.status !== undefined && queryParams.value.status !== '' && queryParams.value.status !== null) {
    queryParams.value.status = Number(queryParams.value.status);
  }
  
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    name: '',
    description: '',
    coverUrl: '',
    coverFileId: undefined,
    coverFile: undefined,
    languageCodes: [],
    level: 1,
    categoryId: undefined,
    author: '',
    translator: '',
    publisher: '',
    status: 0
  };
  proxy.resetForm("bookFormRef");
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加书籍";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const bookId = row.id || ids.value;
  getBook(bookId).then(response => {
    const bookData = response.data;    
    // 确保数据类型正确
    if (bookData.status !== undefined && bookData.status !== null) {
      bookData.status = Number(bookData.status);
    }
    
    // 处理语种数据回显
    if (!bookData.languageCodes && bookData.languages) {
      // 如果后端返回的是languages字段（整数），需要转换为languageCodes数组
      bookData.languageCodes = [];
      
      // 使用二进制位表示解析语种
      const languages = Number(bookData.languages);
      languageOptions.value.forEach(lang => {
        // 检查当前语种的位是否为1
        if (languages & lang.code) {
          bookData.languageCodes.push(lang.code);
        }
      });
    }
    
    // 处理书籍分级数据回显 (从列表数据中获取)
    if (bookData.level === undefined || bookData.level === null) {
      // 尝试从原始行数据获取level
      if (row.level !== undefined) {
        bookData.level = Number(row.level);
      } else {
        bookData.level = 1; // 默认为初级
      }
    } else {
      bookData.level = Number(bookData.level);
    }
    
    // 处理分类数据回显
    // 1. 从列表行数据中获取categoryId
    if ((bookData.categoryId === undefined || bookData.categoryId === null) && row.categoryId) {
      bookData.categoryId = row.categoryId.toString();
    } 
    // 2. 如果没有categoryId但有categoryName，则尝试从categoryOptions中找到对应ID
    else if ((bookData.categoryId === undefined || bookData.categoryId === null) && bookData.categoryName) {
      const category = categoryOptions.value.find(c => c.name === bookData.categoryName);
      if (category) {
        bookData.categoryId = category.id;
      }
    }
    // 3. 确保categoryId是字符串类型
    else if (bookData.categoryId !== undefined && bookData.categoryId !== null) {
      bookData.categoryId = bookData.categoryId.toString();
    }
    
    form.value = bookData;
    open.value = true;
    title.value = "修改书籍";
  });
}

/** 表单提交 */
function submitForm() {
  proxy.$refs["bookFormRef"].validate(valid => {
    if (valid) {
      loading.value = true;
      // 创建数据副本，避免修改原始表单数据
      const submitData = JSON.parse(JSON.stringify(form.value));
      
      // 处理语种数据，将languageCodes转换为二进制整数
      if (submitData.languageCodes && submitData.languageCodes.length > 0) {
        let languages = 0;
        submitData.languageCodes.forEach(code => {
          // 直接使用语种code值进行位或运算
          languages |= code;
        });
        submitData.languages = languages;
      }
      
      // 确保level和categoryId是数值类型
      if (submitData.level !== undefined && submitData.level !== null) {
        submitData.level = Number(submitData.level);
      }
      if (submitData.categoryId !== undefined && submitData.categoryId !== null) {
        submitData.categoryId = Number(submitData.categoryId);
      }
            
      // 根据id判断是修改还是新增
      if (submitData.id !== undefined) {
        updateBook(submitData).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        }).finally(() => {
          loading.value = false;
        });
      } else {
        addBook(submitData).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        }).finally(() => {
          loading.value = false;
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const bookIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除书籍编号为"' + bookIds + '"的数据项?').then(function() {
    return delBook(bookIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 状态修改 */
function handleStatusChange(row, status) {
  const text = status === 1 ? "启用" : "禁用";
  
  proxy.$modal.confirm('确认要"' + text + '""' + row.name + '"书籍吗?').then(function() {
    return changeBookStatus(row.id, status);
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
    getList();
  }).catch(() => {});
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 显示简介对话框 */
function showDescriptionDialog(row) {
  currentDescription.value = {
    name: row.name,
    description: row.description || '暂无简介'
  };
  descriptionDialogVisible.value = true;
}

/** 上传前校验文件类型 */
function beforeCoverUpload(file) {
  const isJPG = file.type === 'image/jpeg';
  if (!isJPG) {
    proxy.$modal.msgError('只能上传JPG格式图片!');
    return false;
  }
  return true;
}

/** 上传封面请求 */
function uploadCoverRequest(options) {
  const file = options.file;
  uploadBookCover(file).then(res => {
    form.value.coverUrl = res.url;
    form.value.coverFileId = res.fileId;
    proxy.$modal.msgSuccess("上传成功");
  }).catch(err => {
    proxy.$modal.msgError("上传失败");
  });
}

/** 获取书籍分类列表 */
function getCategoryList() {
  listBookCategories().then(response => {
    // 确保分类ID为字符串类型，便于下拉框匹配
    categoryOptions.value = response.data.map(item => ({
      ...item,
      id: item.id.toString()
    }));
  }).catch(error => {
    categoryOptions.value = [];
  });
}

/** 获取语言列表 */
function getLanguageList() {
  // 使用固定语言选项，code值直接对应二进制位
  languageOptions.value = [
    { code: 1, name: "中文" },    // 0001 (bit 0)
    { code: 2, name: "俄语" },    // 0010 (bit 1)
    { code: 4, name: "法语" },    // 0100 (bit 2)
    { code: 8, name: "英语" },    // 1000 (bit 3)
    { code: 16, name: "西班牙语" } // 10000 (bit 4)
  ];
}

/** 根据语种代码获取语种名称 */
function getLanguageName(code) {
  const lang = languageOptions.value.find(l => l.code === code);
  return lang ? lang.name : '未知语言';
}

onMounted(() => {
  getList();
  getCategoryList();
  getLanguageList();
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.search-container {
  padding: 20px 0;
  margin-bottom: 20px;
}

.query-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.action-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.button-group {
  display: flex;
  gap: 10px;
}

.common-cell {
  padding: 8px 0;
}

.book-name-cell {
  font-weight: 500;
  color: #303133;
}

.description-cell {
  cursor: pointer;
  color: #409eff;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 40px;
}

.language-preview {
  margin-top: 8px;
  padding: 5px;
  border-radius: 4px;
  background-color: #f8f8f8;
}

.image-container {
  position: relative;
  width: 60px;
  height: 60px;
  margin: 0 auto;
  cursor: pointer;
  overflow: hidden;
  border-radius: 4px;
}

.table-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-hover-text {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 12px;
  padding: 2px 0;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-container:hover .image-hover-text {
  opacity: 1;
}

.image-placeholder, .image-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 12px;
  color: #909399;
}

.upload-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.cover-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 120px;
  height: 160px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
}

.cover-uploader:hover {
  border-color: #409EFF;
}

.cover-upload-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #8c939d;
  font-size: 14px;
  width: 100%;
  height: 100%;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-tip {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
}

.radio-group-full-width {
  width: 100%;
}

.image-preview-title {
  color: #fff;
  font-size: 16px;
  padding: 10px;
  text-align: center;
}

.description-dialog-content {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.description-dialog-text {
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}
</style> 