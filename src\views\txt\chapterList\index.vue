<template>
  <div class="app-container">
    <div class="search-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="query-form">
        <el-form-item label="所属书籍" prop="bookId">
          <el-select
            v-model="queryParams.bookId"
            placeholder="请选择所属书籍"
            clearable
            filterable
            style="width: 240px"
          >
            <el-option
              v-for="book in bookOptions"
              :key="book.id"
              :label="book.name"
              :value="book.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="语种" prop="language">
          <el-select
            v-model="queryParams.language"
            placeholder="请选择语种"
            clearable
            style="width: 200px"
          >
            <el-option label="全部" value="" />
            <el-option label="中文" :value="1" />
            <el-option label="俄语" :value="2" />
            <el-option label="法语" :value="4" />
            <el-option label="英语" :value="8" />
            <el-option label="西班牙语" :value="16" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <div class="query-and-player-container">
      <div class="button-group">
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </div>
      <!-- 音频播放器组件，固定在搜索按钮一行的右侧 -->
      <div class="player-wrapper">
        <audio-player
          :audio-url="playUrl ? [playUrl] : ['']"
          :audio-name="playUrl ? [playName] : ['暂无播放内容']"
          :autoplay="false"
          :single-mode="true"
          @ended="onAudioEnded"
          @play="onAudioPlay"
          @pause="onAudioPause"
          @error="onAudioError"
          ref="audioPlayerRef"
        />
      </div>
    </div>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="chapterList" @selection-change="handleSelectionChange" :row-key="row => row.id">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="章节编号" align="center" prop="id" width="180" />
      <el-table-column label="序号" align="center" prop="number" width="80">
        <template #default="scope">
          <span>{{ scope.row.number || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="章节名称" align="center" prop="name" :show-overflow-tooltip="true" min-width="120">
        <template #default="scope">
          <div class="common-cell">
            <span class="chapter-name-cell" :title="scope.row.name">{{ scope.row.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="语种" align="center" width="120">
        <template #default="scope">
          <div class="language-tags">
            <el-tag v-if="scope.row.language & 1" size="small" type="success" class="language-tag">中文</el-tag>
            <el-tag v-if="scope.row.language & 2" size="small" type="info" class="language-tag">俄语</el-tag>
            <el-tag v-if="scope.row.language & 4" size="small" type="warning" class="language-tag">法语</el-tag>
            <el-tag v-if="scope.row.language & 8" size="small" type="primary" class="language-tag">英语</el-tag>
            <el-tag v-if="scope.row.language & 16" size="small" type="danger" class="language-tag">西班牙语</el-tag>
            <span v-if="!scope.row.language">未知</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="章节原文" align="center" prop="originalTextName" :show-overflow-tooltip="true" width="150">
        <template #default="scope">
          <div v-if="scope.row.originalTextUrl" class="text-preview">
            <el-link type="primary" @click="previewTextFile(scope.row)" :underline="false" class="preview-link">
              <span>{{ scope.row.originalTextName || '查看原文' }}</span>
              <el-icon class="preview-icon"><Document /></el-icon>
            </el-link>
          </div>
          <span v-else>无原文</span>
        </template>
      </el-table-column>
      <el-table-column label="章节字数" align="center" prop="wordCount" width="100">
        <template #default="scope">
          <span>{{ scope.row.wordCount || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="章节音频" align="center" width="120">
        <template #default="scope">
          <el-tooltip :content="scope.row.audioUrl ? '点击试听' : '无音频'" placement="top">
            <div class="play-button-container">
              <el-button 
                circle 
                :icon="currentAudioId === scope.row.id && isPlaying ? 'VideoPause' : 'VideoPlay'" 
                @click="playAudio(scope.row)"
                size="small"
                :disabled="!scope.row.audioUrl"
                :type="!scope.row.audioUrl ? 'info' : (currentAudioId === scope.row.id && isPlaying ? 'danger' : 'primary')"
                class="play-button"
                :class="{'playing': currentAudioId === scope.row.id && isPlaying, 'disabled': !scope.row.audioUrl}"
              />
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100" class-name="small-padding fixed-width">
        <template #default="scope">
          <div class="operation-buttons">
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:chapter:remove']"></el-button>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 原文文件预览对话框 -->
    <el-dialog
      v-model="textPreviewVisible"
      :title="textPreviewTitle"
      width="70%"
      :before-close="closeTextPreview"
      append-to-body
    >
      <div class="text-content-preview" v-loading="textLoading">
        <!-- 使用SRT预览组件 -->
        <srt-preview
          v-if="isSrtFile"
          :content="textPreviewContent"
          :fileName="textPreviewFileName"
          :loading="textLoading"
          :showClose="false"
          @check-issues="handleSrtIssues"
          @download="handleSrtDownload"
          ref="srtPreviewRef"
        />
        
        <!-- 普通文本预览 -->
        <pre v-else-if="textPreviewContent" class="srt-content">{{ textPreviewContent }}</pre>
        <div v-else-if="!textLoading" class="no-content">文件内容为空或无法读取</div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeTextPreview">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ChapterList">
import { listChapters, delChapter, changeChapterStatus, batchChangeChapterStatus } from "@/api/txt/chapter";
import { listBooks } from "@/api/txt/book";
import { parseTime } from '@/utils/ruoyi';
import AudioPlayer from "@/components/AudioPlayer/index.vue";
import SrtPreview from "@/components/SrtPreview/index.vue";
import { ref, reactive, onMounted, getCurrentInstance, toRefs, nextTick } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Document } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

// 数据列表
const chapterList = ref([]);
const bookOptions = ref([]);
const languageOptions = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

// 音频播放相关
const audioPlayerRef = ref(null);
const currentAudioId = ref(null);
const isPlaying = ref(false);
const playUrl = ref("");
const playName = ref("");

// 文本预览相关
const textPreviewVisible = ref(false);
const textPreviewTitle = ref('');
const textPreviewContent = ref('');
const textPreviewFileName = ref('');
const textLoading = ref(false);
const isSrtFile = ref(false);
const srtPreviewRef = ref(null);

// 查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    bookId: undefined,
    language: undefined
  }
});

const { queryParams } = toRefs(data);

/** 页面加载时调用 */
onMounted(() => {
  // 获取书籍列表
  getBookList();
  // 获取章节列表数据
  getList();
  
  // 初始化播放器状态
  initAudioPlayer();
});

/** 初始化音频播放器 */
function initAudioPlayer() {
  // 重置音频相关状态
  currentAudioId.value = null;
  isPlaying.value = false;
  playUrl.value = "";
  playName.value = "";
}

/** 查询书籍列表 */
function getBookList() {
  listBooks({ pageSize: 100 }).then(response => {
    bookOptions.value = response.rows || [];
  });
}

/** 查询章节列表 */
function getList() {
  loading.value = true;
  
  listChapters(queryParams.value).then(response => {
    chapterList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.pageNum = 1;
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  proxy.$router.push('/txt/chapter/form');
}

/** 修改按钮操作 */
function handleUpdate(row) {
  proxy.$router.push({
    path: '/txt/chapter/form',
    query: { id: row.id }
  });
}

/** 修改状态按钮操作 */
function handleStatusChange(row) {
  const newStatus = row.status === 1 ? 0 : 1;
  const statusText = newStatus === 1 ? "启用" : "禁用";
  
  ElMessageBox.confirm(
    `确认要${statusText}章节"${row.name}"吗？`,
    "警告",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(function() {
    return changeChapterStatus({ id: row.id, status: newStatus });
  }).then(() => {
    ElMessage.success(`${statusText}成功`);
    getList();
  }).catch(() => {});
}

/** 批量启用按钮操作 */
function handleBatchEnable() {
  if (ids.value.length === 0) {
    ElMessage.warning("请先选择需要启用的章节");
    return;
  }
  
  ElMessageBox.confirm(
    `确认要批量启用选中的${ids.value.length}个章节吗？`,
    "警告",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(function() {
    return batchChangeChapterStatus({ ids: ids.value, status: 1 });
  }).then(() => {
    ElMessage.success("批量启用成功");
    getList();
  }).catch(() => {});
}

/** 批量禁用按钮操作 */
function handleBatchDisable() {
  if (ids.value.length === 0) {
    ElMessage.warning("请先选择需要禁用的章节");
    return;
  }
  
  ElMessageBox.confirm(
    `确认要批量禁用选中的${ids.value.length}个章节吗？`,
    "警告",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(function() {
    return batchChangeChapterStatus({ ids: ids.value, status: 0 });
  }).then(() => {
    ElMessage.success("批量禁用成功");
    getList();
  }).catch(() => {});
}

/** 删除按钮操作 */
function handleDelete(row) {
  const chapterIds = row.id || ids.value;
  const chapterNames = row.name || "所选章节";
  
  ElMessageBox.confirm(
    `是否确认删除${chapterNames}？`,
    "警告",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(function() {
    return delChapter(chapterIds);
  }).then(() => {
    ElMessage.success("删除成功");
    getList();
  }).catch(() => {});
}

/** 播放音频 */
function playAudio(row) {
  if (!row.audioUrl) {
    ElMessage.info("该章节没有音频");
    return;
  }
  
  // 如果是同一个音频，则切换播放状态
  if (currentAudioId.value === row.id) {
    if (isPlaying.value) {
      audioPlayerRef.value?.pause();
    } else {
      audioPlayerRef.value?.play();
    }
    return;
  }
  
  // 设置当前播放的音频
  currentAudioId.value = row.id;
  playUrl.value = row.audioUrl;
  playName.value = row.name;
  isPlaying.value = true;
  
  // 确保音频加载后立即播放
  nextTick(() => {
    audioPlayerRef.value?.play();
  });
}

/** 音频播放事件 */
function onAudioPlay() {
  isPlaying.value = true;
}

/** 音频暂停事件 */
function onAudioPause() {
  isPlaying.value = false;
}

/** 音频播放结束事件 */
function onAudioEnded() {
  isPlaying.value = false;
  currentAudioId.value = null;
}

/** 音频播放错误事件 */
function onAudioError() {
  isPlaying.value = false;
  // 只有在有音频URL的情况下才显示错误提示
  if (playUrl.value) {
    ElMessage.error("音频播放失败，请检查音频文件是否有效");
  }
}

/** 预览文本文件 */
function previewTextFile(row) {
  if (!row.originalTextUrl) {
    ElMessage.info("该章节没有原文文件");
    return;
  }
  
  textPreviewVisible.value = true;
  textPreviewTitle.value = `原文预览：${row.originalTextName || '章节原文'}`;
  textPreviewContent.value = '';
  textPreviewFileName.value = row.originalTextName || '章节原文.srt';
  textLoading.value = true;
  
  // 判断是否为SRT文件
  isSrtFile.value = row.originalTextName && row.originalTextName.toLowerCase().endsWith('.srt');
  
  // 使用fetch获取文本内容
  fetch(row.originalTextUrl)
    .then(response => {
      if (!response.ok) {
        throw new Error('获取文件内容失败');
      }
      return response.text();
    })
    .then(text => {
      textPreviewContent.value = text;
      textLoading.value = false;
    })
    .catch(error => {
      console.error('获取文件内容失败:', error);
      ElMessage.error('获取文件内容失败');
      textLoading.value = false;
      isSrtFile.value = false;
      textPreviewContent.value = '';
    });
}

/** 处理SRT文件问题检查 */
function handleSrtIssues(issues) {
  if (issues.length > 0) {
    console.log('检测到SRT文件问题:', issues);
  }
}

/** 处理SRT文件下载 */
function handleSrtDownload(fileName) {
  console.log('SRT文件已下载:', fileName);
}

/** 关闭文本预览 */
function closeTextPreview() {
  textPreviewVisible.value = false;
  textPreviewTitle.value = '';
  textPreviewContent.value = '';
  textPreviewFileName.value = '';
  isSrtFile.value = false;
}
</script>

<style lang="scss" scoped>
.search-container {
  margin-bottom: 20px;
}

/* 查询和播放器容器样式 */
.query-and-player-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 10px;
  
  .button-group {
    .el-button {
      margin-left: 0;
      margin-right: 10px;
    }
  }
  
  .player-wrapper {
    flex: 0 0 304px;
    height: 60px;
    position: relative;
    overflow: visible;
    margin: 0;
    
    /* 处理音频播放器的空状态样式 */
    :deep(.empty-status) {
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--el-text-color-secondary);
      font-size: 14px;
      
      .empty-icon {
        margin-right: 8px;
        font-size: 16px;
      }
    }
  }
}

/* 响应式布局调整 */
@media screen and (max-width: 768px) {
  .query-and-player-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .player-wrapper {
    width: 100%;
    flex: 0 0 auto;
  }
}

.query-form {
  display: flex;
  flex-wrap: wrap;
}

.common-cell {
  padding: 5px 10px;
}

.chapter-name-cell {
  display: block;
  word-break: break-all;
  white-space: normal;
  line-height: 1.4;
}

.selected-count-box {
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.language-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 4px;
  
  .language-tag {
    margin: 2px;
  }
}

.play-button-container {
  display: flex;
  justify-content: center;
  
  .play-button {
    transition: all 0.3s;
    
    &.playing {
      animation: pulse 1.5s infinite;
    }
    
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

.text-preview {
  cursor: pointer;
  
  .preview-link {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    
    .preview-icon {
      margin-left: 4px;
      font-size: 14px;
    }
  }
}

.text-content-preview {
  max-height: 500px;
  overflow-y: auto;
  background-color: #f5f7fa;
  border-radius: 4px;
  
  .srt-content {
    padding: 16px;
    white-space: pre-wrap;
    word-break: break-word;
    font-family: 'Courier New', Courier, monospace;
    font-size: 14px;
    line-height: 1.5;
  }
  
  .no-content {
    padding: 16px;
    text-align: center;
    color: #909399;
  }
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

/* 增加表格行高，确保播放按钮完全显示 */
.el-table .el-table__row {
  height: 60px;
}

.el-table .el-table__cell {
  padding-top: 8px;
  padding-bottom: 8px;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
</style> 