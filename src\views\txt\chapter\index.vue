<template>
  <div class="app-container">
    <div class="search-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="query-form">
        <el-form-item label="所属书籍" prop="bookId">
          <el-select
            v-model="queryParams.bookId"
            placeholder="请选择所属书籍"
            clearable
            filterable
            style="width: 240px"
          >
            <el-option
              v-for="book in bookOptions"
              :key="book.id"
              :label="book.name"
              :value="book.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="语种" prop="language">
          <el-select
            v-model="queryParams.language"
            placeholder="请选择语种"
            clearable
            style="width: 200px"
          >
            <el-option label="中文" :value="1" />
            <el-option label="俄语" :value="2" />
            <el-option label="法语" :value="4" />
            <el-option label="英语" :value="8" />
            <el-option label="西班牙语" :value="16" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <div class="query-and-player-container">
      <div class="button-group">
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </div>
      <!-- 音频播放器组件，固定在搜索按钮一行的右侧 -->
      <div class="player-wrapper">
        <audio-player
          :audio-url="playUrl ? [playUrl] : ['']"
          :audio-name="playUrl ? [playName] : ['暂无播放内容']"
          :autoplay="false"
          :single-mode="true"
          @ended="onAudioEnded"
          @play="onAudioPlay"
          @pause="onAudioPause"
          @error="onAudioError"
          ref="audioPlayerRef"
        />
      </div>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:chapter:add']">新增</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="chapterList" @selection-change="handleSelectionChange" :row-key="row => row.id">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="章节编号" align="center" prop="id" width="180" />
      <el-table-column label="序号" align="center" prop="number" width="80">
        <template #default="scope">
          <span>{{ scope.row.number || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="章节名称" align="center" prop="name" :show-overflow-tooltip="true" min-width="120">
        <template #default="scope">
          <div class="common-cell">
            <span class="chapter-name-cell" :title="scope.row.name">{{ scope.row.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="语种" align="center" width="120">
        <template #default="scope">
          <div class="language-tags">
            <el-tag v-if="scope.row.language & 1" size="small" type="success" class="language-tag">中文</el-tag>
            <el-tag v-if="scope.row.language & 2" size="small" type="info" class="language-tag">俄语</el-tag>
            <el-tag v-if="scope.row.language & 4" size="small" type="warning" class="language-tag">法语</el-tag>
            <el-tag v-if="scope.row.language & 8" size="small" type="primary" class="language-tag">英语</el-tag>
            <el-tag v-if="scope.row.language & 16" size="small" type="danger" class="language-tag">西班牙语</el-tag>
            <span v-if="!scope.row.language">未知</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="章节原文" align="center" prop="originalTextName" :show-overflow-tooltip="true" width="150">
        <template #default="scope">
          <div v-if="scope.row.originalTextUrl">
            <span>{{ scope.row.originalTextName || '有原文' }}</span>
          </div>
          <span v-else>无原文</span>
        </template>
      </el-table-column>
      <el-table-column label="章节字数" align="center" prop="wordCount" width="100">
        <template #default="scope">
          <span>{{ scope.row.wordCount || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="章节音频" align="center" width="120">
        <template #default="scope">
          <el-tooltip :content="scope.row.audioUrl ? '点击试听' : '无音频'" placement="top">
            <div class="play-button-container">
              <el-button 
                circle 
                :icon="currentAudioId === scope.row.id && isPlaying ? 'VideoPause' : 'VideoPlay'" 
                @click="playAudio(scope.row)"
                size="small"
                :disabled="!scope.row.audioUrl"
                :type="!scope.row.audioUrl ? 'info' : (currentAudioId === scope.row.id && isPlaying ? 'danger' : 'primary')"
                class="play-button"
                :class="{'playing': currentAudioId === scope.row.id && isPlaying, 'disabled': !scope.row.audioUrl}"
              />
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
        <template #default="scope">
          <div class="operation-buttons">
            <el-tooltip content="编辑" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:chapter:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="预览" placement="top" v-if="scope.row.originalTextUrl">
              <el-button link type="primary" icon="View" @click="previewTextFile(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:chapter:remove']"></el-button>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 原文文件预览对话框 -->
    <el-dialog
      v-model="textPreviewVisible"
      :title="textPreviewTitle"
      width="70%"
      :before-close="closeTextPreview"
      append-to-body
    >
      <div class="text-content-preview" v-loading="textLoading">
        <!-- 使用SRT预览组件 -->
        <srt-preview
          v-if="isSrtFile"
          :content="textPreviewContent"
          :fileName="textPreviewFileName"
          :loading="textLoading"
          :showClose="false"
          @check-issues="handleSrtIssues"
          @download="handleSrtDownload"
          ref="srtPreviewRef"
        />
        
        <!-- 普通文本预览 -->
        <pre v-else-if="textPreviewContent" class="srt-content">{{ textPreviewContent }}</pre>
        <div v-else-if="!textLoading" class="no-content">文件内容为空或无法读取</div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeTextPreview">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增章节对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      title="新增章节"
      width="70%"
      :before-close="closeAddDialog"
      append-to-body
    >
      <el-form
        :model="addFormData"
        :rules="formRules"
        ref="addFormRef"
        label-width="100px"
      >
        <el-form-item label="所属书籍" prop="bookId">
          <el-select
            v-model="addFormData.bookId"
            placeholder="请选择所属书籍"
            clearable
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="book in bookOptions"
              :key="book.id"
              :label="book.name"
              :value="book.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="语种" prop="language">
          <el-select
            v-model="addFormData.language"
            placeholder="请选择语种"
            clearable
            style="width: 100%"
          >
            <el-option label="中文" :value="1" />
            <el-option label="俄语" :value="2" />
            <el-option label="法语" :value="4" />
            <el-option label="英语" :value="8" />
            <el-option label="西班牙语" :value="16" />
          </el-select>
        </el-form-item>
        <el-form-item label="章节名称" prop="name">
          <el-input v-model="addFormData.name" placeholder="请输入章节名称" />
        </el-form-item>
        
        <!-- 音频文件上传 -->
        <el-form-item label="章节音频">
          <div class="upload-wrapper">
            <div class="audio-upload-container" :class="{'is-dragover': isDragover}">
              <div 
                v-if="!addFormData.audioUrl" 
                class="audio-uploader"
                @dragover.prevent="handleDragover('add')"
                @dragleave.prevent="handleDragleave('add')"
                @drop.prevent="handleDrop($event, 'add')"
              >
                <input 
                  type="file" 
                  ref="audioFileInput" 
                  @change="handleAudioUpload($event, 'add')" 
                  accept=".mp3" 
                  style="display:none"
                />
                <div 
                  class="audio-upload-placeholder"
                  @click="triggerAudioUpload('add')"
                  :class="{'uploading': uploadAudioLoading}"
                >
                  <el-icon v-if="!uploadAudioLoading"><Plus /></el-icon>
                  <el-icon v-else class="loading-icon"><Loading /></el-icon>
                  <div class="upload-text">
                    <span>{{ uploadAudioLoading ? '上传中...' : '点击或拖拽文件到此处上传' }}</span>
                    <span class="upload-hint">支持 .mp3 格式文件</span>
                  </div>
                </div>
              </div>
              <div v-else class="audio-preview">
                <div class="audio-info">
                  <el-icon><Document /></el-icon>
                  <span class="audio-name">已上传音频文件</span>
                </div>
                <div class="audio-actions">
                  <el-button type="danger" link @click="removeAudioFile('add')">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        
        <!-- 原文文件上传 -->
        <el-form-item label="章节原文" prop="originalTextUrl">
          <div class="srt-editor-container">
            <srt-subtitle-editor
              ref="addSrtEditorRef"
              height="400px"
              :show-upload-button="false"
              :show-editor-buttons="true"
            />
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeAddDialog">取消</el-button>
          <el-button type="primary" @click="submitAddForm">提交</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改章节对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="修改章节"
      width="70%"
      :before-close="closeEditDialog"
      append-to-body
    >
      <el-form
        :model="editFormData"
        :rules="formRules"
        ref="editFormRef"
        label-width="100px"
      >
        <el-form-item label="所属书籍" prop="bookId">
          <el-select
            v-model="editFormData.bookId"
            placeholder="请选择所属书籍"
            clearable
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="book in bookOptions"
              :key="book.id"
              :label="book.name"
              :value="book.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="语种" prop="language">
          <el-select
            v-model="editFormData.language"
            placeholder="请选择语种"
            clearable
            style="width: 100%"
          >
            <el-option label="中文" :value="1" />
            <el-option label="俄语" :value="2" />
            <el-option label="法语" :value="4" />
            <el-option label="英语" :value="8" />
            <el-option label="西班牙语" :value="16" />
          </el-select>
        </el-form-item>
        <el-form-item label="章节名称" prop="name">
          <el-input v-model="editFormData.name" placeholder="请输入章节名称" />
        </el-form-item>
        
        <!-- 音频文件上传 -->
        <el-form-item label="章节音频">
          <div class="upload-wrapper">
            <div class="audio-upload-container" :class="{'is-dragover': isDragoverEdit}">
              <div 
                v-if="!editFormData.audioUrl" 
                class="audio-uploader"
                @dragover.prevent="handleDragover('edit')"
                @dragleave.prevent="handleDragleave('edit')"
                @drop.prevent="handleDrop($event, 'edit')"
              >
                <input 
                  type="file" 
                  ref="audioFileInputEdit" 
                  @change="handleAudioUpload($event, 'edit')" 
                  accept=".mp3" 
                  style="display:none"
                />
                <div 
                  class="audio-upload-placeholder"
                  @click="triggerAudioUpload('edit')"
                  :class="{'uploading': uploadAudioLoading}"
                >
                  <el-icon v-if="!uploadAudioLoading"><Plus /></el-icon>
                  <el-icon v-else class="loading-icon"><Loading /></el-icon>
                  <div class="upload-text">
                    <span>{{ uploadAudioLoading ? '上传中...' : '点击或拖拽文件到此处上传' }}</span>
                    <span class="upload-hint">支持 .mp3 格式文件</span>
                  </div>
                </div>
              </div>
              <div v-else class="audio-preview">
                <div class="audio-info">
                  <el-icon><Document /></el-icon>
                  <span class="audio-name">已上传音频文件</span>
                </div>
                <div class="audio-actions">
                  <el-button type="danger" link @click="removeAudioFile('edit')">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        
        <!-- 原文文件上传 -->
        <el-form-item label="章节原文" prop="originalTextUrl">
          <div class="srt-editor-container">
            <srt-subtitle-editor
              ref="editSrtEditorRef"
              height="400px"
              :show-upload-button="false"
              :show-editor-buttons="true"
            />
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeEditDialog">取消</el-button>
          <el-button type="primary" @click="submitEditForm">提交</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 文件预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="SRT文件预览"
      width="80%"
      :before-close="closePreviewDialog"
      append-to-body
    >
      <div class="preview-content" v-loading="previewLoading">
        <div v-if="previewType === 'text'">
          <pre class="srt-content">{{ previewContent }}</pre>
        </div>
        <div v-else-if="previewType === 'srt'">
          <srt-preview
            :content="previewContent"
            :fileName="previewFileName"
            :loading="previewLoading"
            :showClose="false"
            @check-issues="handleSrtIssues"
            @download="handleSrtDownload"
            ref="srtPreviewRef"
          />
        </div>
        <div v-else class="no-content">文件类型不支持预览</div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closePreviewDialog">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Chapter">
import { listChapters, getChapter, delChapter, addChapter, updateChapter, getChapterFiles, uploadChapterAudio, uploadChapterSubtitle, deleteChapterFile } from "@/api/txt/chapter";
import { listBooks } from "@/api/txt/book";
import { parseTime } from '@/utils/ruoyi';
import AudioPlayer from "@/components/AudioPlayer/index.vue";
import SrtPreview from "@/components/SrtPreview/index.vue";
import SrtSubtitleEditor from "@/components/SrtSubtitleEditor/index.vue";
import { ref, reactive, onMounted, getCurrentInstance, toRefs, nextTick, computed } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Plus, Loading, Document, VideoPlay, Delete } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

// 数据列表
const chapterList = ref([]);
const bookOptions = ref([]);
const languageOptions = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const selectedRows = ref([]);
const single = ref(true);
const total = ref(0);

// 音频播放相关
const audioPlayerRef = ref(null);
const currentAudioId = ref(null);
const isPlaying = ref(false);
const playUrl = ref("");
const playName = ref("");

// 文本预览相关
const textPreviewVisible = ref(false);
const textPreviewTitle = ref('');
const textPreviewContent = ref('');
const textPreviewFileName = ref('');
const textLoading = ref(false);
const isSrtFile = ref(false);
const srtPreviewRef = ref(null);

// 新增章节对话框
const addDialogVisible = ref(false);
const addFormData = reactive({
  bookId: undefined,
  language: undefined,
  name: '',
  originalTextUrl: '',
  fileId: undefined,
  audioUrl: '',
  audioFileId: undefined,
  duration: undefined
});

// 修改章节对话框
const editDialogVisible = ref(false);
const editFormData = reactive({
  id: undefined,
  bookId: undefined,
  language: undefined,
  name: '',
  originalTextUrl: '',
  fileId: undefined,
  audioUrl: '',
  audioFileId: undefined,
  duration: undefined
});

// 文件上传相关
const addSrtEditorRef = ref(null);
const editSrtEditorRef = ref(null);
const uploadAudioLoading = ref(false);
const uploadSrtLoading = ref(false);
const isDragover = ref(false);
const isDragoverEdit = ref(false);
const srtContent = ref('');
const srtFileName = ref('chapter.srt');
const srtContentEdit = ref('');
const srtFileNameEdit = ref('chapter.srt');

// 表单验证规则
const formRules = {
  bookId: [{ required: true, message: '请选择书籍', trigger: 'change' }],
  language: [{ required: true, message: '请选择语言', trigger: 'change' }],
  name: [{ required: true, message: '请输入章节名称', trigger: 'blur' }]
};

// 文件预览对话框
const previewDialogVisible = ref(false);
const previewContent = ref('');
const previewFileName = ref('');
const previewLoading = ref(false);
const previewType = ref('text'); // 'text' or 'srt'

// 查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    bookId: undefined,
    language: undefined
  }
});

const { queryParams } = toRefs(data);

// 计算属性：是否禁用批量操作按钮
const multiple = computed(() => selectedRows.value.length === 0);

/** 页面加载时调用 */
onMounted(() => {
  // 获取书籍列表
  getBookList();
  // 获取章节列表数据
  getList();
  
  // 初始化播放器状态
  initAudioPlayer();
});

/** 初始化音频播放器 */
function initAudioPlayer() {
  // 重置音频相关状态
  currentAudioId.value = null;
  isPlaying.value = false;
  playUrl.value = "";
  playName.value = "";
}

/** 查询书籍列表 */
function getBookList() {
  listBooks({ pageSize: 100 }).then(response => {
    bookOptions.value = response.rows || [];
  });
}

/** 查询章节列表 */
function getList() {
  loading.value = true;
  
  listChapters(queryParams.value).then(response => {
    chapterList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.pageNum = 1;
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  selectedRows.value = selection;
  single.value = selection.length !== 1;
}

/** 新增按钮操作 */
function handleAdd() {
  resetForm();
  addDialogVisible.value = true;
  
  // 默认初始化一个空的SRT内容
  nextTick(() => {
    if (addSrtEditorRef.value) {
      const emptySrt = "1\n00:00:00,000 --> 00:00:05,000\n请在此处编辑字幕内容";
      addSrtEditorRef.value.parseSrtContent(emptySrt);
      addSrtEditorRef.value.setFileName(srtFileName.value);
      srtContent.value = emptySrt;
    }
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  resetForm();
  const id = row.id || row;
  getChapter(id).then(response => {
    // 获取响应数据
    const data = response.data;
    
    // 将除音频之外的字段赋值给editFormData
    editFormData.id = data.id;
    editFormData.bookId = data.bookId;
    editFormData.language = data.language;
    editFormData.name = data.name;
    editFormData.originalTextUrl = data.originalTextUrl;
    editFormData.originalTextName = data.originalTextName;
    editFormData.fileId = data.fileId;
    editFormData.wordCount = data.wordCount;
    
    // 不回显音频数据，保持与新增页面一致
    // 音频相关字段在resetForm时已经重置为空
    
    editDialogVisible.value = true;
    
    // 初始化SRT编辑器
    nextTick(() => {
      if (editSrtEditorRef.value) {
        // 如果有原文链接，自动加载SRT文件内容到编辑器
        if (editFormData.originalTextUrl) {
          // 设置加载状态
          uploadSrtLoading.value = true;
          
          // 获取文件名
          const fileName = editFormData.originalTextName || 'chapter.srt';
          srtFileNameEdit.value = fileName;
          
          // 使用fetch获取SRT文件内容
          fetch(editFormData.originalTextUrl)
            .then(response => {
              if (!response.ok) {
                throw new Error('文件获取失败');
              }
              return response.text();
            })
            .then(content => {
              srtContentEdit.value = content;
              editSrtEditorRef.value.parseSrtContent(content);
              editSrtEditorRef.value.setFileName(fileName);
            })
            .catch(error => {
              console.error('加载SRT文件失败:', error);
              ElMessage.error('加载SRT文件失败，使用默认模板');
              // 初始化一个空的SRT内容
              const emptySrt = "1\n00:00:00,000 --> 00:00:05,000\n请在此处编辑字幕内容";
              editSrtEditorRef.value.parseSrtContent(emptySrt);
              editSrtEditorRef.value.setFileName(srtFileNameEdit.value);
              srtContentEdit.value = emptySrt;
            })
            .finally(() => {
              uploadSrtLoading.value = false;
            });
        } else {
          // 初始化一个空的SRT内容
          const emptySrt = "1\n00:00:00,000 --> 00:00:05,000\n请在此处编辑字幕内容";
          editSrtEditorRef.value.parseSrtContent(emptySrt);
          editSrtEditorRef.value.setFileName(srtFileNameEdit.value);
          srtContentEdit.value = emptySrt;
        }
      }
    });
  });
}

/** 重置表单 */
function resetForm() {
  // 重置新增表单
  addFormData.bookId = undefined;
  addFormData.language = undefined;
  addFormData.name = '';
  addFormData.originalTextUrl = '';
  addFormData.fileId = undefined;
  addFormData.audioUrl = '';
  addFormData.audioFileId = undefined;
  addFormData.duration = undefined;
  
  // 重置编辑表单
  editFormData.id = undefined;
  editFormData.bookId = undefined;
  editFormData.language = undefined;
  editFormData.name = '';
  editFormData.originalTextUrl = '';
  editFormData.fileId = undefined;
  editFormData.audioUrl = '';
  editFormData.audioFileId = undefined;
  editFormData.duration = undefined;
  
  // 移除表单验证
  if (proxy.$refs.addFormRef) {
    proxy.$refs.addFormRef.resetFields();
  }
  if (proxy.$refs.editFormRef) {
    proxy.$refs.editFormRef.resetFields();
  }
  
  // 重置SRT编辑器
  if (addSrtEditorRef.value) {
    addSrtEditorRef.value.clearEditor();
  }
  if (editSrtEditorRef.value) {
    editSrtEditorRef.value.clearEditor();
  }
}

/** 触发音频文件上传 */
function triggerAudioUpload(formType = 'add') {
  // 如果已经上传了文件，询问是否替换
  if ((formType === 'add' && addFormData.audioUrl) || (formType === 'edit' && editFormData.audioUrl)) {
    ElMessageBox.confirm(
      '已存在上传的音频文件，是否替换？',
      '提示',
      {
        confirmButtonText: '替换',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      if (formType === 'add') {
        proxy.$refs.audioFileInput.click();
      } else {
        proxy.$refs.audioFileInputEdit.click();
      }
    }).catch(() => {});
  } else {
    if (formType === 'add') {
      proxy.$refs.audioFileInput.click();
    } else {
      proxy.$refs.audioFileInputEdit.click();
    }
  }
}

/** 删除已上传的音频文件 */
function removeAudioFile(formType = 'add') {
  ElMessageBox.confirm(
    '确认删除已上传的音频文件吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const fileUrl = formType === 'add' ? addFormData.audioUrl : editFormData.audioUrl;
    const fileId = formType === 'add' ? addFormData.audioFileId : editFormData.audioFileId;
    
    // 如果有文件URL和文件ID，则调用删除API
    if (fileUrl && fileId) {
      loading.value = true;
      deleteChapterFile(fileUrl, fileId).then(response => {
        ElMessage.success('音频文件已删除');
        // 清空表单中的音频数据
        if (formType === 'add') {
          addFormData.audioUrl = '';
          addFormData.audioFileId = undefined;
          addFormData.duration = undefined;
          // 清空文件输入框，确保可以重新选择同一个文件
          if (proxy.$refs.audioFileInput) {
            proxy.$refs.audioFileInput.value = '';
          }
        } else {
          editFormData.audioUrl = '';
          editFormData.audioFileId = undefined;
          editFormData.duration = undefined;
          // 清空文件输入框，确保可以重新选择同一个文件
          if (proxy.$refs.audioFileInputEdit) {
            proxy.$refs.audioFileInputEdit.value = '';
          }
        }
      }).catch(error => {
        console.error('删除音频文件失败:', error);
        ElMessage.error('删除音频文件失败');
      }).finally(() => {
        loading.value = false;
      });
    } else {
      // 如果没有文件URL或文件ID，只清空表单数据
      if (formType === 'add') {
        addFormData.audioUrl = '';
        addFormData.audioFileId = undefined;
        addFormData.duration = undefined;
        // 清空文件输入框
        if (proxy.$refs.audioFileInput) {
          proxy.$refs.audioFileInput.value = '';
        }
      } else {
        editFormData.audioUrl = '';
        editFormData.audioFileId = undefined;
        editFormData.duration = undefined;
        // 清空文件输入框
        if (proxy.$refs.audioFileInputEdit) {
          proxy.$refs.audioFileInputEdit.value = '';
        }
      }
      ElMessage.success('音频文件已清除');
    }
  }).catch(() => {});
}

/** 处理音频文件上传 */
function handleAudioUpload(event, formType = 'add') {
  const file = event.target.files[0];
  if (!file) {
    return;
  }
  
  const isMP3 = file.type === 'audio/mp3' || file.type === 'audio/mpeg' || file.name.toLowerCase().endsWith('.mp3');
  if (!isMP3) {
    ElMessage.error('只支持上传MP3格式的音频文件');
    // 清空文件输入框，确保可以重新选择
    if (formType === 'add' && proxy.$refs.audioFileInput) {
      proxy.$refs.audioFileInput.value = '';
    } else if (formType === 'edit' && proxy.$refs.audioFileInputEdit) {
      proxy.$refs.audioFileInputEdit.value = '';
    }
    return;
  }
  
  // 检查文件大小，限制为100MB
  const maxSize = 100 * 1024 * 1024; // 100MB
  if (file.size > maxSize) {
    ElMessage.error('音频文件大小不能超过100MB');
    // 清空文件输入框
    if (formType === 'add' && proxy.$refs.audioFileInput) {
      proxy.$refs.audioFileInput.value = '';
    } else if (formType === 'edit' && proxy.$refs.audioFileInputEdit) {
      proxy.$refs.audioFileInputEdit.value = '';
    }
    return;
  }
  
  uploadAudioLoading.value = true;
  
  uploadChapterAudio(file).then(response => {
    if (formType === 'add') {
      addFormData.audioUrl = response.url;
      addFormData.audioFileId = response.fileId;
      addFormData.duration = response.duration;
    } else {
      editFormData.audioUrl = response.url;
      editFormData.audioFileId = response.fileId;
      editFormData.duration = response.duration;
    }
    ElMessage.success('音频上传成功');
  }).catch(error => {
    console.error('上传音频失败:', error);
    ElMessage.error('上传音频失败');
    // 清空文件输入框
    if (formType === 'add' && proxy.$refs.audioFileInput) {
      proxy.$refs.audioFileInput.value = '';
    } else if (formType === 'edit' && proxy.$refs.audioFileInputEdit) {
      proxy.$refs.audioFileInputEdit.value = '';
    }
  }).finally(() => {
    uploadAudioLoading.value = false;
  });
}

/** 处理SRT文件上传 */
function handleSrtUpload(event, formType = 'add') {
  const file = event.target.files[0];
  if (!file) {
    return;
  }
  
  const isSRT = file.type === 'application/x-subrip' || file.name.toLowerCase().endsWith('.srt');
  if (!isSRT) {
    ElMessage.error('只支持上传SRT格式的字幕文件');
    return;
  }
  
  uploadSrtLoading.value = true;
  
  uploadChapterSubtitle(file).then(response => {
    if (formType === 'add') {
      addFormData.originalTextUrl = response.url;
      addFormData.fileId = response.fileId;
      
      // 读取文件内容到SRT编辑器
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target.result;
        if (addSrtEditorRef.value) {
          addSrtEditorRef.value.parseSrtContent(content);
          addSrtEditorRef.value.setFileName(file.name);
        }
      };
      reader.readAsText(file);
    } else {
      editFormData.originalTextUrl = response.url;
      editFormData.fileId = response.fileId;
      
      // 读取文件内容到SRT编辑器
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target.result;
        if (editSrtEditorRef.value) {
          editSrtEditorRef.value.parseSrtContent(content);
          editSrtEditorRef.value.setFileName(file.name);
        }
      };
      reader.readAsText(file);
    }
    ElMessage.success('字幕文件上传成功');
  }).catch(error => {
    console.error('上传字幕失败:', error);
    ElMessage.error('上传字幕失败');
  }).finally(() => {
    uploadSrtLoading.value = false;
  });
}

/** 提交新增表单 */
async function submitAddForm() {
  try {
    // 表单验证
    const valid = await new Promise(resolve => {
      proxy.$refs.addFormRef.validate(valid => resolve(valid));
    });
    
    if (!valid) return;
    
    // 设置全局加载状态
    loading.value = true;
    
    // 处理SRT文件上传
    if (addSrtEditorRef.value) {
      // 验证SRT内容
      const isValid = addSrtEditorRef.value.validateSrtContent();
      if (!isValid) {
        ElMessage.warning('SRT文件格式有误，请检查后再提交');
        loading.value = false;
        return;
      }
      
      // 获取SRT内容和文件名
      srtContent.value = addSrtEditorRef.value.getSrtContent();
      srtFileName.value = addSrtEditorRef.value.getFileName() || 'chapter.srt';
      
      // 创建SRT文件对象
      const srtBlob = new Blob([srtContent.value], { type: 'text/plain' });
      const srtFile = new File([srtBlob], srtFileName.value, { type: 'text/plain' });
      
      // 设置上传加载状态
      uploadSrtLoading.value = true;
      
      try {
        // 上传SRT文件
        const response = await uploadChapterSubtitle(srtFile);
        addFormData.originalTextUrl = response.url;
        addFormData.fileId = response.fileId;
      } catch (error) {
        console.error('上传字幕失败:', error);
        ElMessage.error('上传字幕失败，请重试');
        loading.value = false;
        uploadSrtLoading.value = false;
        return;
      } finally {
        uploadSrtLoading.value = false;
      }
    }
    
    // 提交章节信息
    await addChapter(addFormData);
    ElMessage.success('新增成功');
    addDialogVisible.value = false;
    getList();
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error('提交失败，请重试');
  } finally {
    loading.value = false;
  }
}

/** 提交编辑表单 */
async function submitEditForm() {
  try {
    // 表单验证
    const valid = await new Promise(resolve => {
      proxy.$refs.editFormRef.validate(valid => resolve(valid));
    });
    
    if (!valid) return;
    
    // 设置全局加载状态
    loading.value = true;
    
    // 处理SRT文件上传
    if (editSrtEditorRef.value) {
      // 验证SRT内容
      const isValid = editSrtEditorRef.value.validateSrtContent();
      if (!isValid) {
        ElMessage.warning('SRT文件格式有误，请检查后再提交');
        loading.value = false;
        return;
      }
      
      // 获取SRT内容和文件名
      srtContentEdit.value = editSrtEditorRef.value.getSrtContent();
      srtFileNameEdit.value = editSrtEditorRef.value.getFileName() || 'chapter.srt';
      
      // 创建SRT文件对象
      const srtBlob = new Blob([srtContentEdit.value], { type: 'text/plain' });
      const srtFile = new File([srtBlob], srtFileNameEdit.value, { type: 'text/plain' });
      
      // 设置上传加载状态
      uploadSrtLoading.value = true;
      
      try {
        // 上传SRT文件
        const response = await uploadChapterSubtitle(srtFile);
        editFormData.originalTextUrl = response.url;
        editFormData.fileId = response.fileId;
      } catch (error) {
        console.error('上传字幕失败:', error);
        ElMessage.error('上传字幕失败，请重试');
        loading.value = false;
        uploadSrtLoading.value = false;
        return;
      } finally {
        uploadSrtLoading.value = false;
      }
    }
    
    // 提交章节信息
    await updateChapter(editFormData);
    ElMessage.success('修改成功');
    editDialogVisible.value = false;
    getList();
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error('提交失败，请重试');
  } finally {
    loading.value = false;
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  const chapterIds = row.id || selectedRows.value.map(item => item.id);
  const chapterNames = row.name || "所选章节";
  
  ElMessageBox.confirm(
    `是否确认删除${chapterNames}？`,
    "警告",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(function() {
    return delChapter(chapterIds);
  }).then(() => {
    ElMessage.success("删除成功");
    getList();
  }).catch(() => {});
}

/** 播放音频 */
function playAudio(row) {
  if (!row.audioUrl) {
    ElMessage.info("该章节没有音频");
    return;
  }
  
  // 如果是同一个音频，则切换播放状态
  if (currentAudioId.value === row.id) {
    if (isPlaying.value) {
      audioPlayerRef.value?.pause();
    } else {
      audioPlayerRef.value?.play();
    }
    return;
  }
  
  // 设置当前播放的音频
  currentAudioId.value = row.id;
  playUrl.value = row.audioUrl;
  playName.value = row.name;
  isPlaying.value = true;
  
  // 确保音频加载后立即播放
  nextTick(() => {
    audioPlayerRef.value?.play();
  });
}

/** 音频播放事件 */
function onAudioPlay() {
  isPlaying.value = true;
}

/** 音频暂停事件 */
function onAudioPause() {
  isPlaying.value = false;
}

/** 音频播放结束事件 */
function onAudioEnded() {
  isPlaying.value = false;
  currentAudioId.value = null;
}

/** 音频播放错误事件 */
function onAudioError() {
  isPlaying.value = false;
  // 只有在有音频URL的情况下才显示错误提示
  if (playUrl.value) {
    ElMessage.error("音频播放失败，请检查音频文件是否有效");
  }
}

/** 预览文本文件 */
function previewTextFile(row) {
  if (!row || !row.originalTextUrl) {
    ElMessage.warning('没有可预览的文件');
    return;
  }
  
  previewLoading.value = true;
  previewFileName.value = row.name || '未命名文件';
  previewDialogVisible.value = true;
  
  // 判断文件类型
  const fileUrl = row.originalTextUrl;
  const isSrtFile = fileUrl.toLowerCase().endsWith('.srt');
  previewType.value = isSrtFile ? 'srt' : 'text';
  
  // 使用fetch API获取文件内容
  fetch(fileUrl)
    .then(response => {
      if (!response.ok) {
        throw new Error('文件获取失败');
      }
      return response.text();
    })
    .then(data => {
      previewContent.value = data;
      previewLoading.value = false;
    })
    .catch(error => {
      console.error('预览文件失败:', error);
      previewLoading.value = false;
      previewContent.value = '';
      ElMessage.error('文件预览失败，请检查文件链接是否有效');
    });
}

/** 处理SRT文件问题检查 */
function handleSrtIssues(issues) {
  if (issues && issues.length > 0) {
    ElMessage.warning(`检测到${issues.length}个时间轴问题`);
  } else {
    ElMessage.success('未检测到时间轴问题');
  }
}

/** 处理SRT文件下载 */
function handleSrtDownload(fileName, content) {
  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  link.click();
  URL.revokeObjectURL(url);
}

/** 关闭文本预览 */
function closeTextPreview() {
  textPreviewVisible.value = false;
  textPreviewTitle.value = '';
  textPreviewContent.value = '';
  textPreviewFileName.value = '';
  isSrtFile.value = false;
}

/** 关闭新增章节对话框 */
function closeAddDialog() {
  addDialogVisible.value = false;
  srtContent.value = '';
}

/** 关闭修改章节对话框 */
function closeEditDialog() {
  editDialogVisible.value = false;
  srtContentEdit.value = '';
}

/** 关闭文件预览对话框 */
function closePreviewDialog() {
  previewDialogVisible.value = false;
  previewContent.value = '';
  previewFileName.value = '';
  previewLoading.value = false;
}

/** 处理文件拖拽 */
function handleDragover(formType = 'add') {
  if (formType === 'add') {
    isDragover.value = true;
  } else {
    isDragoverEdit.value = true;
  }
}

/** 处理文件拖拽离开 */
function handleDragleave(formType = 'add') {
  if (formType === 'add') {
    isDragover.value = false;
  } else {
    isDragoverEdit.value = false;
  }
}

/** 处理文件拖放 */
function handleDrop(event, formType = 'add') {
  if (formType === 'add') {
    isDragover.value = false;
  } else {
    isDragoverEdit.value = false;
  }
  
  const files = event.dataTransfer.files;
  if (files.length) {
    const file = files[0];
    
    // 检查文件类型
    if (!file.name.toLowerCase().endsWith('.srt')) {
      ElMessage.error('只支持上传SRT格式的字幕文件');
      return;
    }
    
    // 读取文件内容
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target.result;
      
      if (formType === 'add') {
        srtContent.value = content;
        srtFileName.value = file.name;
        
        nextTick(() => {
          if (addSrtEditorRef.value) {
            addSrtEditorRef.value.parseSrtContent(content);
            addSrtEditorRef.value.setFileName(file.name);
          }
        });
      } else {
        srtContentEdit.value = content;
        srtFileNameEdit.value = file.name;
        
        nextTick(() => {
          if (editSrtEditorRef.value) {
            editSrtEditorRef.value.parseSrtContent(content);
            editSrtEditorRef.value.setFileName(file.name);
          }
        });
      }
    };
    reader.readAsText(file);
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  // ... existing code ...
}

.search-container {
  margin-bottom: 20px;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.preview-content {
  min-height: 300px;
  max-height: 600px;
  overflow-y: auto;
  padding: 10px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.srt-content {
  margin: 0;
  padding: 10px;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: Consolas, Monaco, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

.no-content {
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 10px;
}

.mb8 {
  margin-bottom: 8px;
}

/* 查询和播放器容器样式 */
.query-and-player-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 10px;
  
  .button-group {
    .el-button {
      margin-left: 0;
      margin-right: 10px;
    }
  }
  
  .player-wrapper {
    flex: 0 0 304px;
    height: 60px;
    position: relative;
    overflow: visible;
    margin: 0;
    
    /* 处理音频播放器的空状态样式 */
    :deep(.empty-status) {
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--el-text-color-secondary);
      font-size: 14px;
      
      .empty-icon {
        margin-right: 8px;
        font-size: 16px;
      }
    }
  }
}

/* 响应式布局调整 */
@media screen and (max-width: 768px) {
  .query-and-player-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .player-wrapper {
    width: 100%;
    flex: 0 0 auto;
  }
}

.query-form {
  display: flex;
  flex-wrap: wrap;
}

.common-cell {
  padding: 5px 10px;
}

.chapter-name-cell {
  display: block;
  word-break: break-all;
  white-space: normal;
  line-height: 1.4;
}

.selected-count-box {
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.language-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 4px;
  
  .language-tag {
    margin: 2px;
  }
}

.play-button-container {
  display: flex;
  justify-content: center;
  
  .play-button {
    transition: all 0.3s;
    
    &.playing {
      animation: pulse 1.5s infinite;
    }
    
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

.text-preview {
  cursor: pointer;
  
  .preview-link {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    
    .preview-icon {
      margin-left: 4px;
      font-size: 14px;
    }
  }
}

.text-content-preview {
  max-height: 500px;
  overflow-y: auto;
  background-color: #f5f7fa;
  border-radius: 4px;
  
  .srt-content {
    padding: 16px;
    white-space: pre-wrap;
    word-break: break-word;
    font-family: 'Courier New', Courier, monospace;
    font-size: 14px;
    line-height: 1.5;
  }
  
  .no-content {
    padding: 16px;
    text-align: center;
    color: #909399;
  }
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

/* 增加表格行高，确保播放按钮完全显示 */
.el-table .el-table__row {
  height: 60px;
}

.el-table .el-table__cell {
  padding-top: 8px;
  padding-bottom: 8px;
}

/* 文件上传相关样式 */
.upload-container {
  width: 100%;
}

.upload-button-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-info {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 5px;
}

.srt-editor-container {
  margin-top: 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #f8f9fa;
  width: 100%;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* 音频上传组件样式 */
.upload-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.audio-upload-container {
  width: 100%;
  margin-bottom: 8px;
  display: flex;
}

.audio-upload-container.is-dragover .audio-upload-placeholder {
  border-color: #409EFF;
  background-color: rgba(64, 158, 255, 0.06);
  color: #409EFF;
}

.audio-uploader {
  width: 100%;
  height: 70px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.audio-upload-placeholder {
  width: 100%;
  height: 100%;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #8c939d;
  transition: all 0.3s;
  padding: 10px;
}

.audio-upload-placeholder:hover {
  border-color: #409EFF;
  color: #409EFF;
}

.audio-upload-placeholder .el-icon {
  font-size: 24px;
  margin-right: 0;
  margin-bottom: 8px;
}

.upload-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  line-height: 1.5;
}

.upload-hint {
  font-size: 12px;
  margin-top: 4px;
  color: #909399;
}

.audio-upload-placeholder.uploading {
  background-color: #f5f7fa;
  color: #409EFF;
  border-color: #c0c4cc;
}

.loading-icon {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.audio-preview {
  width: 100%;
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  padding: 10px 15px;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.audio-info {
  display: flex;
  align-items: center;
  color: #67c23a;
  font-size: 14px;
}

.audio-info .el-icon {
  margin-right: 8px;
}

.audio-name {
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.audio-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  width: 300px;
  text-align: center;
}
</style> 